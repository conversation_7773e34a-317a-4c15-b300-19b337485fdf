,task_id,problem,seed,success,test_cases_passed,test_cases_total,runtime,local_tests,local_test_state,iteration_idx
0,minimum-time-to-visit-a-cell-in-a-grid,"
from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    visited = set()
    
    while pq:
        time, row, col = heapq.heappop(pq)
        
        if (row, col) in visited:
            continue
        
        visited.add((row, col))
        
        if row == m - 1 and col == n - 1:
            return time
        
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n and (new_row, new_col) not in visited:
                new_time = max(time + 1, grid[new_row][new_col])
                heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1
",55,False,22,41,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minimumTime([[0,1,3,2],[5,1,2,5],[4,3,8,6]]) == 7 # ERROR: This unit test fails. Output was 6, but expected value was: 7
assert minimumTime([[0,2,4],[3,2,1],[1,0,4]]) == -1 # ERROR: This unit test fails. Output was 5, but expected value was: -1","(False, False)",0
1,minimum-time-to-visit-a-cell-in-a-grid,"from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    visited = set()
    
    while pq:
        time, row, col = heapq.heappop(pq)
        
        if (row, col) in visited:
            continue
        
        visited.add((row, col))
        
        if row == m - 1 and col == n - 1:
            return time
        
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n and (new_row, new_col) not in visited:
                new_time = max(time + 1, grid[new_row][new_col])
                heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1",55,False,22,41,,,,1
2,find-the-string-with-lcp,"
def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    word = [''] * n
    current_char = 'a'
    
    for i in range(n):
        if word[i] == '':
            word[i] = current_char
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    word[j] = word[i]
            current_char = chr(ord(current_char) + 1)
    
    for i in range(n):
        for j in range(n):
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    return ''.join(word)
",55,False,36,55,,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
3,find-the-string-with-lcp,"from typing import List

def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    word = [''] * n
    current_char = 'a'
    
    # Check for symmetry and diagonal values
    for i in range(n):
        if lcp[i][i] != n - i:
            return """"
        for j in range(n):
            if lcp[i][j] != lcp[j][i]:
                return """"
            if lcp[i][j] > n - max(i, j):
                return """"
    
    for i in range(n):
        if word[i] == '':
            word[i] = current_char
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    word[j] = word[i]
            current_char = chr(ord(current_char) + 1)
            if current_char > 'z':
                return """"
    
    for i in range(n):
        for j in range(n):
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    return ''.join(word)",55,False,36,55,,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
4,handling-sum-queries-after-update,"
from typing import List

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    result = []
    
    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            for i in range(l, r + 1):
                nums1[i] = 1 - nums1[i]
        elif query[0] == 2:
            p = query[1]
            for i in range(len(nums2)):
                nums2[i] += nums1[i] * p
        elif query[0] == 3:
            result.append(sum(nums2))
    
    return result
",55,False,72,77,,"**Tests that the code passed:**

assert handleQuery([1,0,1], [0,0,0], [[1,1,1],[2,1,0],[3,0,0]]) == [3]
assert handleQuery([1], [5], [[2,0,0],[3,0,0]]) == [5]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
5,handling-sum-queries-after-update,"from typing import List

class SegmentTree:
    def __init__(self, n):
        self.n = n
        self.tree = [0] * (4 * n)
        self.lazy = [0] * (4 * n)
        self.nums1_state = [0] * n  # Auxiliary array to maintain the state of nums1

    def update_range(self, start, end, l, r, node):
        if self.lazy[node] != 0:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > end or start > r or end < l:
            return

        if start >= l and end <= r:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            for i in range(start, end + 1):
                self.nums1_state[i] ^= 1  # Update the state of nums1
            return

        mid = (start + end) // 2
        self.update_range(start, mid, l, r, node * 2 + 1)
        self.update_range(mid + 1, end, l, r, node * 2 + 2)
        self.tree[node] = self.tree[node * 2 + 1] + self.tree[node * 2 + 2]

    def query(self, start, end, l, r, node):
        if self.lazy[node] != 0:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > end or start > r or end < l:
            return 0

        if start >= l and end <= r:
            return self.tree[node]

        mid = (start + end) // 2
        left_query = self.query(start, mid, l, r, node * 2 + 1)
        right_query = self.query(mid + 1, end, l, r, node * 2 + 2)
        return left_query + right_query

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    n = len(nums1)
    seg_tree = SegmentTree(n)
    result = []
    cumulative_p = 0
    sum_nums2 = sum(nums2)

    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            seg_tree.update_range(0, n - 1, l, r, 0)
        elif query[0] == 2:
            p = query[1]
            for i in range(n):
                if seg_tree.nums1_state[i] == 1:
                    nums2[i] += p
            sum_nums2 = sum(nums2)  # Update the running sum of nums2
        elif query[0] == 3:
            result.append(sum_nums2)

    return result",55,False,0,77,,,,1
6,subsequence-with-the-minimum-score,"
def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Find the longest prefix of t that is a subsequence of s
    prefix = [0] * (m + 1)
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            j += 1
        prefix[j] = i + 1
    
    # Find the longest suffix of t that is a subsequence of s
    suffix = [0] * (m + 1)
    j = 0
    for i in range(n - 1, -1, -1):
        if j < m and s[i] == t[m - 1 - j]:
            j += 1
        suffix[j] = n - i
    
    # Calculate the minimum score
    min_score = m
    for i in range(m + 1):
        if prefix[i] + suffix[m - i] <= n:
            min_score = min(min_score, m - i)
    
    return min_score
",55,False,7,54,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minimumScore(""abacaba"", ""bzaa"") == 1 # ERROR: This unit test fails. Output was 0, but expected value was: 1
assert minimumScore(""cde"", ""xyz"") == 3 # ERROR: This unit test fails. Output was 0, but expected value was: 3","(False, False)",0
7,subsequence-with-the-minimum-score,"def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Find the longest prefix of t that is a subsequence of s
    prefix = [-1] * (m + 1)
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            j += 1
            prefix[j] = i
    
    # Find the longest suffix of t that is a subsequence of s
    suffix = [-1] * (m + 1)
    j = 0
    for i in range(n - 1, -1, -1):
        if j < m and s[i] == t[m - 1 - j]:
            j += 1
            suffix[j] = i
    
    # Calculate the minimum score
    min_score = m
    for i in range(m + 1):
        if prefix[i] != -1:
            for j in range(m - i + 1):
                if suffix[j] != -1 and prefix[i] < suffix[j]:
                    left = prefix[i] + 1
                    right = suffix[j] - 1
                    min_score = min(min_score, right - left + 1)
    
    # Handle edge case where t is already a subsequence of s
    if min_score == m:
        min_score = 0
    
    # Handle edge case where all characters of t need to be removed
    if min_score == m and not any(suffix):
        min_score = m
    
    return min_score",55,False,14,54,,,,1
8,minimum-number-of-visited-cells-in-a-grid,"
from typing import List
from collections import deque

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    if m == 1 and n == 1:
        return 1
    
    directions = [(0, 1), (1, 0)]
    queue = deque([(0, 0, 1)])  # (row, col, steps)
    visited = set((0, 0))
    
    while queue:
        i, j, steps = queue.popleft()
        
        for di, dj in directions:
            for k in range(1, grid[i][j] + 1):
                ni, nj = i + di * k, j + dj * k
                if 0 <= ni < m and 0 <= nj < n and (ni, nj) not in visited:
                    if ni == m - 1 and nj == n - 1:
                        return steps + 1
                    queue.append((ni, nj, steps + 1))
                    visited.add((ni, nj))
    
    return -1
",55,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
9,minimum-number-of-visited-cells-in-a-grid,"from typing import List
from collections import deque
import heapq

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    if m == 1 and n == 1:
        return 1

    def explore_moves(i, j, steps, queue, visited):
        for di, dj in [(0, 1), (1, 0)]:
            if di == 0:  # Rightward movement
                for nj in range(j + 1, min(n, j + grid[i][j] + 1)):
                    if (i, nj) not in visited:
                        if i == m - 1 and nj == n - 1:
                            return steps + 1
                        heapq.heappush(queue, (steps + 1, i, nj))
                        visited.add((i, nj))
            else:  # Downward movement
                for ni in range(i + 1, min(m, i + grid[i][j] + 1)):
                    if (ni, j) not in visited:
                        if ni == m - 1 and j == n - 1:
                            return steps + 1
                        heapq.heappush(queue, (steps + 1, ni, j))
                        visited.add((ni, j))
        return None

    queue = [(1, 0, 0)]  # (steps, row, col)
    visited = set([(0, 0)])

    while queue:
        steps, i, j = heapq.heappop(queue)
        result = explore_moves(i, j, steps, queue, visited)
        if result is not None:
            return result

    return -1",55,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
10,rearranging-fruits,"
from typing import List
from collections import Counter

def minCost(basket1: List[int], basket2: List[int]) -> int:
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(basket1 + basket2):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the minimum cost to make both baskets equal
    swaps = []
    for fruit in set(basket1 + basket2):
        diff = abs(count1[fruit] - count2[fruit]) // 2
        if count1[fruit] > count2[fruit]:
            swaps.extend([fruit] * diff)
        elif count2[fruit] > count1[fruit]:
            swaps.extend([fruit] * diff)
    
    swaps.sort()
    min_cost = 0
    for i in range(len(swaps) // 2):
        min_cost += swaps[i]
    
    return min_cost
",55,False,20,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
11,rearranging-fruits,"from typing import List
from collections import Counter
import heapq

def minCost(basket1: List[int], basket2: List[int]) -> int:
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(count1.keys()).union(set(count2.keys())):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the minimum cost to make both baskets equal
    swaps = []
    for fruit in set(count1.keys()).union(set(count2.keys())):
        diff = abs(count1[fruit] - count2[fruit]) // 2
        if count1[fruit] > count2[fruit]:
            swaps.extend([fruit] * diff)
        elif count2[fruit] > count1[fruit]:
            swaps.extend([fruit] * diff)
    
    # Use a heap to find the smallest half of the swap costs
    heapq.heapify(swaps)
    min_cost = 0
    for _ in range(len(swaps) // 2):
        min_cost += heapq.heappop(swaps)
    
    return min_cost",55,False,20,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
12,lexicographically-smallest-beautiful-string,"
def smallestBeautifulString(s: str, k: int) -> str:
    def is_beautiful(s: str) -> bool:
        for i in range(len(s) - 1):
            if s[i] == s[i + 1]:
                return False
        for i in range(len(s) - 2):
            if s[i] == s[i + 2]:
                return False
        return True

    n = len(s)
    s = list(s)
    for i in range(n - 1, -1, -1):
        for j in range(ord(s[i]) - ord('a') + 1, k):
            s[i] = chr(ord('a') + j)
            if is_beautiful(s[:i + 1]):
                for l in range(i + 1, n):
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s[:l + 1]):
                            break
                return ''.join(s)
    return """"
",55,False,1042,1054,,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",0
13,lexicographically-smallest-beautiful-string,"def smallestBeautifulString(s: str, k: int) -> str:
    def is_beautiful(s: str, pos: int) -> bool:
        if pos > 0 and s[pos] == s[pos - 1]:
            return False
        if pos > 1 and s[pos] == s[pos - 2]:
            return False
        return True

    n = len(s)
    s = list(s)
    for i in range(n - 1, -1, -1):
        for j in range(ord(s[i]) - ord('a') + 1, k):
            s[i] = chr(ord('a') + j)
            if is_beautiful(s, i):
                valid = True
                for l in range(i + 1, n):
                    found = False
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s, l):
                            found = True
                            break
                    if not found:
                        valid = False
                        break
                if valid:
                    return ''.join(s)
    return """"",55,True,1054,1054,249 ms,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",1
14,count-increasing-quadruplets,"
def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    for i in range(n - 3):
        for j in range(i + 1, n - 2):
            for k in range(j + 1, n - 1):
                for l in range(k + 1, n):
                    if nums[i] < nums[k] < nums[j] < nums[l]:
                        count += 1
                        
    return count
",55,False,63,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2
assert countQuadruplets([1,2,3,4]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
15,count-increasing-quadruplets,"from typing import List

def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    # Use a more efficient approach to count valid quadruplets
    for i in range(n - 3):
        for j in range(i + 1, n - 2):
            for k in range(j + 1, n - 1):
                if nums[i] < nums[k]:
                    for l in range(k + 1, n):
                        if nums[k] < nums[j] < nums[l]:
                            count += 1
    
    return count",55,False,66,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2
assert countQuadruplets([1,2,3,4]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
16,put-marbles-in-bags,"
from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    if k == 1:
        return 0
    
    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the minimum and maximum possible scores
    costs.sort()
    
    # The minimum score is the sum of the smallest k-1 costs
    min_score = sum(costs[:k-1])
    
    # The maximum score is the sum of the largest k-1 costs
    max_score = sum(costs[-(k-1):])
    
    return max_score - min_score
",55,True,103,103,541 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
17,put-marbles-in-bags,"from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    if k == 1 or k == n:
        return 0
    
    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the minimum and maximum possible scores
    costs.sort()
    
    # The minimum score is the sum of the smallest k-1 costs
    min_score = sum(costs[:k-1])
    
    # The maximum score is the sum of the largest k-1 costs
    max_score = sum(costs[-(k-1):])
    
    return max_score - min_score",55,True,103,103,549 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
18,shortest-cycle-in-a-graph,"
from typing import List
from collections import deque, defaultdict

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    def bfs(start):
        dist = [-1] * n
        dist[start] = 0
        queue = deque([(start, -1)])
        while queue:
            node, parent = queue.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    queue.append((neighbor, node))
                elif neighbor != parent:
                    return dist[node] + dist[neighbor] + 1
        return float('inf')
    
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i))
    
    return shortest_cycle if shortest_cycle != float('inf') else -1
",55,False,84,88,,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
19,shortest-cycle-in-a-graph,"from typing import List
from collections import deque, defaultdict
import heapq

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    def bfs(start):
        dist = [-1] * n
        dist[start] = 0
        queue = deque([(start, -1)])
        shortest_cycle = float('inf')
        while queue:
            node, parent = queue.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    queue.append((neighbor, node))
                elif neighbor != parent:
                    shortest_cycle = min(shortest_cycle, dist[node] + dist[neighbor] + 1)
        return shortest_cycle
    
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i))
    
    return shortest_cycle if shortest_cycle != float('inf') else -1",55,True,88,88,1422 ms,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
20,minimum-time-to-complete-all-tasks,"
from typing import List

def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = [0] * 2001
    total_time = 0

    for start, end, duration in tasks:
        current_duration = sum(time_on[start:end+1])
        remaining_duration = duration - current_duration

        if remaining_duration > 0:
            for t in range(end, start-1, -1):
                if time_on[t] == 0:
                    time_on[t] = 1
                    total_time += 1
                    remaining_duration -= 1
                    if remaining_duration == 0:
                        break

    return total_time
",55,True,1059,1059,303 ms,"**Tests that the code passed:**

assert findMinimumTime([[2,3,1],[4,5,1],[1,5,2]]) == 2
assert findMinimumTime([[1,3,2],[2,5,3],[5,6,2]]) == 4

**Tests that the code failed:**

No tests failed.
","(True, True)",0
21,minimum-time-to-complete-all-tasks,"from typing import List

def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = [0] * 2001
    total_time = 0

    for start, end, duration in tasks:
        current_duration = sum(time_on[start:end+1])
        remaining_duration = duration - current_duration

        if remaining_duration > 0:
            for t in range(end, start-1, -1):
                if time_on[t] == 0:
                    time_on[t] = 1
                    total_time += 1
                    remaining_duration -= 1
                    if remaining_duration == 0:
                        break

    return total_time",55,True,1059,1059,305 ms,"**Tests that the code passed:**

assert findMinimumTime([[2,3,1],[4,5,1],[1,5,2]]) == 2
assert findMinimumTime([[1,3,2],[2,5,3],[5,6,2]]) == 4

**Tests that the code failed:**

No tests failed.
","(True, True)",1
22,count-number-of-possible-root-nodes,"
from collections import defaultdict, deque
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node)

    def count_correct_guesses(root):
        correct_count = 0
        for u, v in guesses:
            if parent_map[v] == u:
                correct_count += 1
        return correct_count

    n = len(edges) + 1
    tree = build_tree(edges)
    parent_map = [-1] * n

    # Arbitrarily choose node 0 as the initial root
    dfs(0, -1)

    possible_roots = 0
    for i in range(n):
        if count_correct_guesses(i) >= k:
            possible_roots += 1

    return possible_roots
",55,False,35,51,,"**Tests that the code passed:**

assert rootCount([[0,1],[1,2],[2,3],[3,4]], [[1,0],[3,4],[2,1],[3,2]], 1) == 5

**Tests that the code failed:**

assert rootCount([[0,1],[1,2],[1,3],[4,2]], [[1,3],[0,1],[1,0],[2,4]], 3) == 3 # ERROR: This unit test fails. Output was 5, but expected value was: 3","(False, True)",0
23,count-number-of-possible-root-nodes,"from collections import defaultdict, deque
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent, parent_map):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node, parent_map)

    def count_correct_guesses(root, parent_map):
        correct_count = 0
        for u, v in guesses:
            if parent_map[v] == u:
                correct_count += 1
        return correct_count

    n = len(edges) + 1
    tree = build_tree(edges)
    possible_roots = 0

    for i in range(n):
        parent_map = [-1] * n
        dfs(i, -1, parent_map)
        if count_correct_guesses(i, parent_map) >= k:
            possible_roots += 1

    return possible_roots",55,False,0,0,-1,"**Tests that the code passed:**

assert rootCount([[0,1],[1,2],[1,3],[4,2]], [[1,3],[0,1],[1,0],[2,4]], 3) == 3
assert rootCount([[0,1],[1,2],[2,3],[3,4]], [[1,0],[3,4],[2,1],[3,2]], 1) == 5

**Tests that the code failed:**

No tests failed.
","(True, True)",1
24,number-of-ways-to-earn-points,"
from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] will be the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # We need to update dp array for each type of question
        new_dp = dp[:]
        for t in range(target + 1):
            for k in range(1, count + 1):
                if t + k * marks <= target:
                    new_dp[t + k * marks] = (new_dp[t + k * marks] + dp[t]) % MOD
                else:
                    break
        dp = new_dp
    
    return dp[target]
",55,True,63,63,1424 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
25,number-of-ways-to-earn-points,"from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] will be the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # We need to update dp array for each type of question
        new_dp = dp[:]
        for current_score in range(target + 1):
            for question_count in range(1, count + 1):
                if current_score + question_count * marks <= target:
                    new_dp[current_score + question_count * marks] = (new_dp[current_score + question_count * marks] + dp[current_score]) % MOD
                else:
                    break
        dp = new_dp
    
    return dp[target]",55,True,63,63,1403 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
26,split-the-array-to-make-coprime-products,"
from typing import List
from math import gcd
from functools import reduce

def findValidSplit(nums: List[int]) -> int:
    def product(arr):
        return reduce(lambda x, y: x * y, arr, 1)
    
    n = nums.length
    for i in range(n - 1):
        left_product = product(nums[:i + 1])
        right_product = product(nums[i + 1:])
        if gcd(left_product, right_product) == 1:
            return i
    return -1
",55,False,29,75,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert findValidSplit([4,7,8,15,3,5]) == 2 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.
assert findValidSplit([4,7,15,8,3,5]) == -1 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.","(False, False)",0
27,split-the-array-to-make-coprime-products,"from typing import List
from math import gcd
from functools import reduce

def findValidSplit(nums: List[int]) -> int:
    def product(arr):
        return reduce(lambda x, y: x * y, arr, 1)
    
    n = len(nums)
    left_product = 1
    right_product = product(nums)
    
    for i in range(n - 1):
        left_product *= nums[i]
        right_product //= nums[i]
        if gcd(left_product, right_product) == 1:
            return i
    return -1",55,False,31,75,,"**Tests that the code passed:**

assert findValidSplit([4,7,8,15,3,5]) == 2
assert findValidSplit([4,7,15,8,3,5]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
28,time-to-cross-a-bridge,"
from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = sorted([(i, t[0] + t[2], t[0], t[1], t[2], t[3]) for i, t in enumerate(time)], key=lambda x: (x[1], x[0]))
    left_waiting = [(0, i) for i in range(k)]
    right_waiting = []
    left_working = []
    right_working = []
    current_time = 0
    boxes_moved = 0

    while boxes_moved < n or right_working or right_waiting:
        while left_working and left_working[0][0] <= current_time:
            _, i = heapq.heappop(left_working)
            heapq.heappush(left_waiting, (current_time, i))
        
        while right_working and right_working[0][0] <= current_time:
            _, i = heapq.heappop(right_working)
            heapq.heappush(right_waiting, (current_time, i))
        
        if right_waiting:
            _, i = heapq.heappop(right_waiting)
            current_time += workers[i][4]
            heapq.heappush(left_working, (current_time + workers[i][5], i))
            boxes_moved += 1
        elif left_waiting and boxes_moved < n:
            _, i = heapq.heappop(left_waiting)
            current_time += workers[i][2]
            heapq.heappush(right_working, (current_time + workers[i][3], i))
        else:
            if left_working:
                current_time = left_working[0][0]
            elif right_working:
                current_time = right_working[0][0]

    return current_time
",55,False,6,65,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert findCrossingTime(1, 3, [[1,1,2,1],[1,1,3,1],[1,1,4,1]]) == 6 # ERROR: This unit test fails. Output was 7, but expected value was: 6
assert findCrossingTime(3, 2, [[1,9,1,8],[10,10,10,10]]) == 50 # ERROR: This unit test fails. Output was 32, but expected value was: 50","(False, False)",0
29,time-to-cross-a-bridge,"from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = sorted([(i, t[0] + t[2], t[0], t[1], t[2], t[3]) for i, t in enumerate(time)], key=lambda x: (x[1], x[0]))
    left_waiting = [(0, i) for i in range(k)]
    right_waiting = []
    left_working = []
    right_working = []
    current_time = 0
    boxes_moved = 0
    last_worker_time = 0

    while boxes_moved < n or right_working or right_waiting:
        while left_working and left_working[0][0] <= current_time:
            _, i = heapq.heappop(left_working)
            heapq.heappush(left_waiting, (current_time, i))
        
        while right_working and right_working[0][0] <= current_time:
            _, i = heapq.heappop(right_working)
            heapq.heappush(right_waiting, (current_time, i))
        
        if right_waiting:
            _, i = heapq.heappop(right_waiting)
            current_time += workers[i][4]
            heapq.heappush(left_working, (current_time + workers[i][5], i))
            boxes_moved += 1
            last_worker_time = current_time
        elif left_waiting and boxes_moved < n:
            _, i = heapq.heappop(left_waiting)
            current_time += workers[i][2]
            heapq.heappush(right_working, (current_time + workers[i][3], i))
        else:
            if right_waiting:
                current_time = right_waiting[0][0]
            elif left_working and right_working:
                current_time = min(left_working[0][0], right_working[0][0])
            elif left_working:
                current_time = left_working[0][0]
            elif right_working:
                current_time = right_working[0][0]
            else:
                break

    return last_worker_time  # Correct final time calculation",55,False,6,65,,,,1
30,check-if-point-is-reachable,"
def isReachable(targetX: int, targetY: int) -> bool:
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

    # Reduce targetX and targetY by dividing by 2 until they are odd
    while targetX % 2 == 0:
        targetX //= 2
    while targetY % 2 == 0:
        targetY //= 2

    # Check if the gcd of the reduced targetX and targetY is 1
    return gcd(targetX, targetY) == 1

# Example usage:
# print(isReachable(6, 9))  # Output: False
# print(isReachable(4, 7))  # Output: True
",55,True,60,60,40 ms,"**Tests that the code passed:**

assert isReachable(6, 9) == False
assert isReachable(4, 7) == True

**Tests that the code failed:**

No tests failed.
","(True, True)",0
31,check-if-point-is-reachable,"from math import gcd

def isReachable(targetX: int, targetY: int) -> bool:
    # If the GCD of targetX and targetY is a power of 2, then the point (targetX, targetY) is reachable from (1, 1)
    def isPowerOfTwo(n):
        return (n & (n - 1)) == 0 and n > 0

    return isPowerOfTwo(gcd(targetX, targetY))

# Example usage:
# print(isReachable(6, 9))  # Output: False
# print(isReachable(4, 7))  # Output: True",55,True,60,60,35 ms,"**Tests that the code passed:**

assert isReachable(6, 9) == False
assert isReachable(4, 7) == True

**Tests that the code failed:**

No tests failed.
","(True, True)",1
32,minimum-cost-to-split-an-array,"
from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    def importance_value(subarray: List[int]) -> int:
        count = defaultdict(int)
        for num in subarray:
            count[num] += 1
        trimmed_length = sum(v for v in count.values() if v > 1)
        return k + trimmed_length

    n = len(nums)
    dp = [float('inf')] * (n + 1)
    dp[0] = 0

    for i in range(1, n + 1):
        for j in range(i):
            dp[i] = min(dp[i], dp[j] + importance_value(nums[j:i]))

    return dp[n]
",55,False,34,59,,"**Tests that the code passed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8
assert minCost([1,2,1,2,1], 2) == 6
assert minCost([1,2,1,2,1], 5) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
33,minimum-cost-to-split-an-array,"from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    def importance_value(subarray: List[int], freq: defaultdict) -> int:
        trimmed_length = sum(v for v in freq.values() if v > 1)
        return k + trimmed_length

    n = len(nums)
    dp = [float('inf')] * (n + 1)
    dp[0] = 0

    for i in range(1, n + 1):
        freq = defaultdict(int)
        for j in range(i, 0, -1):
            freq[nums[j-1]] += 1
            dp[i] = min(dp[i], dp[j-1] + importance_value(nums[j-1:i], freq))

    return dp[n]",55,False,42,59,,"**Tests that the code passed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8
assert minCost([1,2,1,2,1], 2) == 6
assert minCost([1,2,1,2,1], 5) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
34,difference-between-maximum-and-minimum-price-sum,"
from typing import List

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    from collections import defaultdict, deque
    
    # Build the adjacency list for the tree
    tree = defaultdict(list)
    for a, b in edges:
        tree[a].append(b)
        tree[b].append(a)
    
    # Function to perform DFS and calculate the maximum and minimum path sums
    def dfs(node, parent):
        max_sum = price[node]
        min_sum = price[node]
        for neighbor in tree[node]:
            if neighbor == parent:
                continue
            child_max, child_min = dfs(neighbor, node)
            max_sum = max(max_sum, price[node] + child_max)
            min_sum = min(min_sum, price[node] + child_min)
        return max_sum, min_sum
    
    max_cost = 0
    # Try rooting the tree at each node and calculate the cost
    for root in range(n):
        max_sum, min_sum = dfs(root, -1)
        max_cost = max(max_cost, max_sum - min_sum)
    
    return max_cost
",55,False,37,58,,"**Tests that the code passed:**

assert maxOutput(6, [[0,1],[1,2],[1,3],[3,4],[3,5]], [9,8,7,6,10,5]) == 24
assert maxOutput(3, [[0,1],[1,2]], [1,1,1]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
35,difference-between-maximum-and-minimum-price-sum,"from typing import List
from collections import defaultdict

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    # Build the adjacency list for the tree
    tree = defaultdict(list)
    for a, b in edges:
        tree[a].append(b)
        tree[b].append(a)
    
    # Function to perform DFS and calculate the maximum and minimum path sums
    def dfs(node, parent):
        max_sum = price[node]
        min_sum = price[node]
        for neighbor in tree[node]:
            if neighbor == parent:
                continue
            child_max, child_min = dfs(neighbor, node)
            max_sum = max(max_sum, price[node] + child_max)
            min_sum = min(min_sum, price[node] + child_min)
        return max_sum, min_sum
    
    # Memoization to store intermediate results
    memo = {}
    
    def memoized_dfs(node, parent):
        if (node, parent) in memo:
            return memo[(node, parent)]
        max_sum, min_sum = dfs(node, parent)
        memo[(node, parent)] = (max_sum, min_sum)
        return max_sum, min_sum
    
    max_cost = 0
    # Try rooting the tree at each node and calculate the cost
    for root in range(n):
        max_sum, min_sum = memoized_dfs(root, -1)
        max_cost = max(max_cost, max_sum - min_sum)
    
    return max_cost",55,False,37,58,,"**Tests that the code passed:**

assert maxOutput(6, [[0,1],[1,2],[1,3],[3,4],[3,5]], [9,8,7,6,10,5]) == 24
assert maxOutput(3, [[0,1],[1,2]], [1,1,1]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",1
36,maximize-the-minimum-powered-city,"
from typing import List

def maxPower(stations: List[int], r: int, k: int) -> int:
    def canAchieve(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        
        for i in range(len(stations)):
            if i > r:
                current_power -= additional_stations[i - r - 1]
            
            current_power += stations[i]
            
            if current_power < min_power:
                needed = min_power - current_power
                if total_additional + needed > k:
                    return False
                additional_stations[min(i + r, len(stations) - 1)] += needed
                current_power += needed
                total_additional += needed
        
        return True
    
    left, right = 0, sum(stations) + k
    while left < right:
        mid = (left + right + 1) // 2
        if canAchieve(mid):
            left = mid
        else:
            right = mid - 1
    
    return left
",55,False,2,30,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert maxPower([1,2,4,5,0], 1, 2) == 5 # ERROR: This unit test fails. Output was 3, but expected value was: 5
assert maxPower([4,4,4,4], 0, 3) == 4 # ERROR: This unit test fails. Output was 7, but expected value was: 4","(False, False)",0
37,maximize-the-minimum-powered-city,"from typing import List

def maxPower(stations: List[int], r: int, k: int) -> int:
    def canAchieve(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        window_sum = sum(stations[:r+1])
        
        for i in range(len(stations)):
            if i > r:
                window_sum -= stations[i - r - 1] + additional_stations[i - r - 1]
            if i + r < len(stations):
                window_sum += stations[i + r] + additional_stations[i + r]
            
            if window_sum < min_power:
                needed = min_power - window_sum
                if total_additional + needed > k:
                    return False
                if i + r < len(stations):
                    additional_stations[i + r] += needed
                    window_sum += needed
                total_additional += needed
        
        return True
    
    left, right = 0, sum(stations) + k
    while left < right:
        mid = (left + right + 1) // 2
        if canAchieve(mid):
            left = mid
        else:
            right = mid - 1
    
    return left",55,False,4,30,,,,1
38,count-anagrams,"
from collections import Counter
from math import factorial

MOD = 10**9 + 7

def countAnagrams(s: str) -> int:
    def count_word_anagrams(word):
        freq = Counter(word)
        word_len = len(word)
        denom = 1
        for count in freq.values():
            denom *= factorial(count)
        return factorial(word_len) // denom

    words = s.split()
    result = 1
    for word in words:
        result *= count_word_anagrams(word)
        result %= MOD

    return result

# Example usage:
# print(countAnagrams(""too hot""))  # Output: 18
# print(countAnagrams(""aa""))       # Output: 1
",55,False,0,41,,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
39,count-anagrams,"from collections import Counter
from math import factorial

MOD = 10**9 + 7
MAX_LEN = 10**5

# Precompute factorials and modular inverses
factorials = [1] * (MAX_LEN + 1)
inv_factorials = [1] * (MAX_LEN + 1)

for i in range(2, MAX_LEN + 1):
    factorials[i] = factorials[i - 1] * i % MOD

inv_factorials[MAX_LEN] = pow(factorials[MAX_LEN], MOD - 2, MOD)
for i in range(MAX_LEN - 1, 0, -1):
    inv_factorials[i] = inv_factorials[i + 1] * (i + 1) % MOD

def countAnagrams(s: str) -> int:
    def count_word_anagrams(word):
        freq = Counter(word)
        word_len = len(word)
        denom = 1
        for count in freq.values():
            denom = denom * inv_factorials[count] % MOD
        return factorials[word_len] * denom % MOD

    words = s.split()
    result = 1
    for word in words:
        result = result * count_word_anagrams(word) % MOD

    return result

# Example usage:
# print(countAnagrams(""too hot""))  # Output: 18
# print(countAnagrams(""aa""))       # Output: 1",55,False,0,41,,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
40,number-of-great-partitions,"
from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    dp = [0] * (total_sum + 1)
    dp[0] = 1
    
    for num in nums:
        for j in range(total_sum, num - 1, -1):
            dp[j] = (dp[j] + dp[j - num]) % MOD
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp[i]) % MOD
    
    return result
",55,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
41,number-of-great-partitions,"from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    # Use a dictionary to store only necessary states to reduce space complexity
    dp = {0: 1}
    
    for num in nums:
        new_dp = dp.copy()
        for j in dp:
            if j + num <= total_sum:
                new_dp[j + num] = (new_dp.get(j + num, 0) + dp[j]) % MOD
        dp = new_dp
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp.get(i, 0)) % MOD
    
    return result",55,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
42,cycle-length-queries-in-a-tree,"
def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node):
        path = []
        while node > 0:
            path.append(node)
            node //= 2
        return path

    def find_cycle_length(a, b):
        path_a = find_path_to_root(a)
        path_b = find_path_to_root(b)
        set_a = set(path_a)
        set_b = set(path_b)
        common_ancestor = max(set_a & set_b)
        return path_a.index(common_ancestor) + path_b.index(common_ancestor) + 1

    result = []
    for a, b in queries:
        result.append(find_cycle_length(a, b))
    return result
",55,True,27,27,1321 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
43,cycle-length-queries-in-a-tree,"from typing import List

def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node):
        path = []
        while node > 0:
            path.append(node)
            node //= 2
        return path

    def find_cycle_length(a, b):
        if a == b // 2 or b == a // 2:
            return 2
        path_a = find_path_to_root(a)
        path_b = find_path_to_root(b)
        set_a = set(path_a)
        set_b = set(path_b)
        common_ancestor = max(set_a & set_b)
        return path_a.index(common_ancestor) + path_b.index(common_ancestor) + 1

    # Precompute paths to root for all nodes
    paths = {i: find_path_to_root(i) for i in range(1, 2 * n)}

    result = []
    for a, b in queries:
        result.append(find_cycle_length(a, b))
    return result",55,True,27,27,1342 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
44,add-edges-to-make-degrees-of-all-nodes-even,"
from typing import List
from collections import defaultdict

def isPossible(n: int, edges: List[List[int]]) -> bool:
    degree = [0] * (n + 1)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    odd_degree_nodes = [i for i in range(1, n + 1) if degree[i] % 2 == 1]

    if len(odd_degree_nodes) == 0:
        return True
    if len(odd_degree_nodes) == 2:
        return True
    if len(odd_degree_nodes) == 4:
        u, v, w, x = odd_degree_nodes
        existing_edges = set((min(a, b), max(a, b)) for a, b in edges)
        if (min(u, v), max(u, v)) not in existing_edges and (min(w, x), max(w, x)) not in existing_edges:
            return True
        if (min(u, w), max(u, w)) not in existing_edges and (min(v, x), max(v, x)) not in existing_edges:
            return True
        if (min(u, x), max(u, x)) not in existing_edges and (min(v, w), max(v, w)) not in existing_edges:
            return True
    return False
",55,False,47,52,,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True
assert isPossible(4, [[1,2],[1,3],[1,4]]) == False

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
45,add-edges-to-make-degrees-of-all-nodes-even,"from typing import List

def isPossible(n: int, edges: List[List[int]]) -> bool:
    """"""
    Determines if it is possible to make the degree of each node in an undirected graph even
    by adding at most two additional edges.

    Parameters:
    n (int): The number of nodes in the graph.
    edges (List[List[int]]): A list of edges where each edge is represented by a list of two integers.

    Returns:
    bool: True if it is possible to make all node degrees even, otherwise False.
    """"""
    degree = [0] * (n + 1)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    odd_degree_nodes = [i for i in range(1, n + 1) if degree[i] % 2 == 1]

    if len(odd_degree_nodes) == 0:
        return True
    if len(odd_degree_nodes) > 4:
        return False
    if len(odd_degree_nodes) == 2:
        return True
    if len(odd_degree_nodes) == 4:
        node1, node2, node3, node4 = odd_degree_nodes
        existing_edges = {(min(a, b), max(a, b)) for a, b in edges}
        if (min(node1, node2), max(node1, node2)) not in existing_edges and (min(node3, node4), max(node3, node4)) not in existing_edges:
            return True
        if (min(node1, node3), max(node1, node3)) not in existing_edges and (min(node2, node4), max(node2, node4)) not in existing_edges:
            return True
        if (min(node1, node4), max(node1, node4)) not in existing_edges and (min(node2, node3), max(node2, node3)) not in existing_edges:
            return True
    return False",55,False,47,52,,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True
assert isPossible(4, [[1,2],[1,3],[1,4]]) == False

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
46,minimum-total-cost-to-make-arrays-unequal,"
from typing import List

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    swaps = []
    
    for i in range(n):
        if nums1[i] == nums2[i]:
            swaps.append(i)
    
    if not swaps:
        return 0
    
    swaps.sort()
    total_cost = 0
    
    for i in range(len(swaps)):
        total_cost += swaps[i]
        if i > 0 and swaps[i] == swaps[i-1]:
            return -1
    
    return total_cost
",55,False,79,104,,"**Tests that the code passed:**

assert minimumTotalCost([1,2,3,4,5], [1,2,3,4,5]) == 10

**Tests that the code failed:**

assert minimumTotalCost([2,2,2,1,3], [1,2,2,3,3]) == 10 # ERROR: This unit test fails. Output was 7, but expected value was: 10
assert minimumTotalCost([1,2,2], [1,2,2]) == -1 # ERROR: This unit test fails. Output was 3, but expected value was: -1","(True, False, False)",0
47,minimum-total-cost-to-make-arrays-unequal,"from typing import List
from collections import deque

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    swaps = []
    total_cost = 0
    
    # Identify indices where nums1[i] == nums2[i]
    for i in range(n):
        if nums1[i] == nums2[i]:
            swaps.append(i)
    
    # If no swaps are needed, check if nums1[i] != nums2[i] for all i
    if not swaps:
        for i in range(n):
            if nums1[i] == nums2[i]:
                return -1
        return 0
    
    # Use BFS to explore all possible swap combinations
    queue = deque([(nums1, 0, swaps)])
    visited = set()
    
    while queue:
        current_nums1, current_cost, current_swaps = queue.popleft()
        state = tuple(current_nums1)
        
        if state in visited:
            continue
        visited.add(state)
        
        # Check if the condition nums1[i] != nums2[i] is met for all i
        if all(current_nums1[i] != nums2[i] for i in range(n)):
            return current_cost
        
        # Generate all possible swaps
        for i in range(n):
            for j in range(i + 1, n):
                if current_nums1[i] != nums2[j] and current_nums1[j] != nums2[i]:
                    new_nums1 = current_nums1[:]
                    new_nums1[i], new_nums1[j] = new_nums1[j], new_nums1[i]
                    new_cost = current_cost + i + j
                    new_swaps = current_swaps[:]
                    if i in new_swaps:
                        new_swaps.remove(i)
                    if j in new_swaps:
                        new_swaps.remove(j)
                    queue.append((new_nums1, new_cost, new_swaps))
    
    return -1",55,False,0,104,,,,1
48,maximum-number-of-points-from-grid-queries,"
from typing import List
from collections import deque

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    def bfs(query):
        m, n = len(grid), len(grid[0])
        visited = [[False] * n for _ in range(m)]
        queue = deque([(0, 0)])
        points = 0
        
        while queue:
            x, y = queue.popleft()
            if visited[x][y]:
                continue
            visited[x][y] = True
            
            if grid[x][y] < query:
                points += 1
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < m and 0 <= ny < n and not visited[nx][ny]:
                        queue.append((nx, ny))
        
        return points
    
    return [bfs(query) for query in queries]
",55,False,17,21,,"**Tests that the code passed:**

assert maxPoints([[1,2,3],[2,5,7],[3,5,1]], [5,6,2]) == [5,8,1]
assert maxPoints([[5,2,1],[1,1,2]], [3]) == [0]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
49,maximum-number-of-points-from-grid-queries,"from typing import List
from collections import deque

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    def bfs(query):
        m, n = len(grid), len(grid[0])
        visited = [[False] * n for _ in range(m)]
        queue = deque([(0, 0)])
        points = 0
        
        while queue:
            x, y = queue.popleft()
            if visited[x][y]:
                continue
            visited[x][y] = True
            
            if grid[x][y] < query:
                points += 1
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < m and 0 <= ny < n and not visited[nx][ny]:
                        queue.append((nx, ny))
        
        return points
    
    def preprocess():
        m, n = len(grid), len(grid[0])
        max_points = [[0] * n for _ in range(m)]
        for i in range(m):
            for j in range(n):
                if i > 0:
                    max_points[i][j] = max(max_points[i][j], max_points[i-1][j])
                if j > 0:
                    max_points[i][j] = max(max_points[i][j], max_points[i][j-1])
                max_points[i][j] += 1 if grid[i][j] < queries[0] else 0
        return max_points
    
    max_points = preprocess()
    
    return [bfs(query) for query in queries]",55,False,17,21,,"**Tests that the code passed:**

assert maxPoints([[1,2,3],[2,5,7],[3,5,1]], [5,6,2]) == [5,8,1]
assert maxPoints([[5,2,1],[1,1,2]], [3]) == [0]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
50,divide-nodes-into-the-maximum-number-of-groups,"
from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1
        
        return max_depth + 1
    
    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    visited_global = set()
    max_groups = 0
    
    for node in range(1, n + 1):
        if node not in visited_global:
            result = bfs(node)
            if result == -1:
                return -1
            max_groups += result
            visited_global.update(graph[node])
    
    return max_groups
",55,False,11,55,,"**Tests that the code passed:**

assert magnificentSets(3, [[1,2],[2,3],[3,1]]) == -1

**Tests that the code failed:**

assert magnificentSets(6, [[1,2],[1,4],[1,5],[2,6],[2,3],[4,6]]) == 4 # ERROR: This unit test fails. Output was 11, but expected value was: 4","(False, True)",0
51,divide-nodes-into-the-maximum-number-of-groups,"from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1, visited
        
        return max_depth + 1, visited
    
    def explore_all_groupings():
        max_groups = 0
        visited_global = set()
        
        for node in range(1, n + 1):
            if node not in visited_global:
                result, visited = bfs(node)
                if result == -1:
                    return -1
                max_groups = max(max_groups, result)
                visited_global.update(visited.keys())
        
        return max_groups
    
    def detect_cycle_and_color(node):
        stack = [(node, 0)]
        colors[node] = 0
        
        while stack:
            current, current_color = stack.pop()
            for neighbor in graph[current]:
                if neighbor not in colors:
                    colors[neighbor] = 1 - current_color
                    stack.append((neighbor, 1 - current_color))
                elif colors[neighbor] == current_color:
                    return False
        return True
    
    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    colors = {}
    for node in range(1, n + 1):
        if node not in colors:
            if not detect_cycle_and_color(node):
                return -1
    
    return explore_all_groupings()",55,False,23,55,,,,1
52,count-palindromic-subsequences,"
def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Base case: single character palindromes
    for i in range(n):
        dp[i][i][1] = 1
    
    # Fill the dp table
    for length in range(2, n + 1):  # length of the substring
        for i in range(n - length + 1):
            j = i + length - 1
            for k in range(1, 6):  # length of the palindromic subsequence
                if s[i] == s[j]:
                    if k == 2:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + 1) % MOD
                    else:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + dp[i + 1][j - 1][k]) % MOD
                dp[i][j][k] = (dp[i][j][k] + dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
    
    # Sum up all palindromic subsequences of length 5
    result = 0
    for i in range(n):
        for j in range(i, n):
            result = (result + dp[i][j][5]) % MOD
    
    return result
",55,False,0,63,,"**Tests that the code passed:**

assert countPalindromes(""103301"") == 2

**Tests that the code failed:**

assert countPalindromes(""0000000"") == 21 # ERROR: This unit test fails. Output was 39, but expected value was: 21
assert countPalindromes(""9999900000"") == 2 # ERROR: This unit test fails. Output was 12, but expected value was: 2","(True, False, False)",0
53,count-palindromic-subsequences,"from collections import defaultdict

def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    if n < 5:
        return 0
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Base case: single character palindromes
    for i in range(n):
        dp[i][i][1] = 1
    
    # Fill the dp table
    for length in range(2, n + 1):  # length of the substring
        for i in range(n - length + 1):
            j = i + length - 1
            for k in range(2, 6):  # length of the palindromic subsequence
                if s[i] == s[j]:
                    if k == 2:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + 1) % MOD
                    else:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + dp[i + 1][j - 1][k]) % MOD
                dp[i][j][k] = (dp[i][j][k] + dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
    
    # Sum up all palindromic subsequences of length 5
    result = 0
    for i in range(n):
        for j in range(i, n):
            result = (result + dp[i][j][5]) % MOD
    
    return result",55,False,11,63,,,,1
54,count-subarrays-with-median-k,"
def countSubarrays(nums: List[int], k: int) -> int:
    def count_medians(arr, k):
        count = 0
        balance = 0
        balance_count = {0: 1}
        for num in arr:
            if num < k:
                balance -= 1
            elif num > k:
                balance += 1
            else:
                count += balance_count.get(balance, 0) + balance_count.get(balance - 1, 0)
            balance_count[balance] = balance_count.get(balance, 0) + 1
        return count

    k_index = nums.index(k)
    left_count = count_medians(nums[:k_index], k)
    right_count = count_medians(nums[k_index:], k)
    
    return left_count + right_count
",55,False,10,45,,"**Tests that the code passed:**

assert countSubarrays([2,3,1], 3) == 1

**Tests that the code failed:**

assert countSubarrays([3,2,1,4,5], 4) == 3 # ERROR: This unit test fails. Output was 1, but expected value was: 3","(False, True)",0
55,count-subarrays-with-median-k,"from typing import List

def countSubarrays(nums: List[int], k: int) -> int:
    k_index = nums.index(k)
    total_count = 0
    balance = 0
    balance_count = {0: 1}
    
    for i in range(len(nums)):
        if nums[i] < k:
            balance -= 1
        elif nums[i] > k:
            balance += 1
        
        if i >= k_index:
            total_count += balance_count.get(balance, 0) + balance_count.get(balance - 1, 0)
        
        if i < k_index:
            balance_count[balance] = balance_count.get(balance, 0) + 1

    return total_count",55,True,45,45,366 ms,,,1
56,number-of-beautiful-partitions,"
def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    n = len(s)
    
    # Helper function to check if a substring is valid
    def is_valid(start, end):
        return s[start] in prime_digits and s[end] not in prime_digits
    
    # dp[i][j] will be the number of ways to partition the first i characters into j parts
    dp = [[0] * (k + 1) for _ in range(n + 1)]
    dp[0][0] = 1
    
    for i in range(1, n + 1):
        for j in range(1, k + 1):
            for l in range(minLength, i + 1):
                if is_valid(i - l, i - 1):
                    dp[i][j] = (dp[i][j] + dp[i - l][j - 1]) % MOD
    
    return dp[n][k]
",55,False,36,74,,"**Tests that the code passed:**

assert beautifulPartitions(""23542185131"", 3, 2) == 3
assert beautifulPartitions(""23542185131"", 3, 3) == 1
assert beautifulPartitions(""3312958"", 3, 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
57,number-of-beautiful-partitions,"from functools import lru_cache

def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    n = len(s)
    
    # Precompute prime and non-prime status for each digit in s
    is_prime = [s[i] in prime_digits for i in range(n)]
    
    # Helper function to check if a substring is valid
    def is_valid(start, end):
        return is_prime[start] and not is_prime[end]
    
    # Use memoization to store intermediate results
    @lru_cache(None)
    def dp(i, j):
        if j == 0:
            return 1 if i == 0 else 0
        if i < j * minLength:
            return 0
        result = 0
        for l in range(minLength, i + 1):
            if is_valid(i - l, i - 1):
                result = (result + dp(i - l, j - 1)) % MOD
        return result
    
    return dp(n, k)",55,False,63,74,,"**Tests that the code passed:**

assert beautifulPartitions(""23542185131"", 3, 2) == 3
assert beautifulPartitions(""23542185131"", 3, 3) == 1
assert beautifulPartitions(""3312958"", 3, 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
58,split-message-based-on-limit,"
def splitMessage(message: str, limit: int) -> List[str]:
    from typing import List
    
    def get_suffix_length(parts: int) -> int:
        return len(f""<{parts}/{parts}>"")
    
    def can_split_into_parts(parts: int) -> bool:
        suffix_length = get_suffix_length(parts)
        total_length = 0
        for i in range(1, parts + 1):
            part_length = limit - len(f""<{i}/{parts}>"")
            if part_length <= 0:
                return False
            total_length += part_length
        return total_length >= len(message)
    
    n = len(message)
    for parts in range(1, n + 1):
        if can_split_into_parts(parts):
            result = []
            index = 0
            for i in range(1, parts + 1):
                suffix = f""<{i}/{parts}>""
                part_length = limit - len(suffix)
                part = message[index:index + part_length]
                result.append(part + suffix)
                index += part_length
            return result
    
    return []
",55,False,55,94,,"**Tests that the code passed:**

assert splitMessage(""this is really a very awesome message"", 9) == [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""]
assert splitMessage(""short message"", 15) == [""short mess<1/2>"",""age<2/2>""]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
59,split-message-based-on-limit,"def splitMessage(message: str, limit: int) -> List[str]:
    from typing import List
    
    def get_suffix_length(parts: int) -> int:
        return len(f""<{parts}/{parts}>"")
    
    def can_split_into_parts(parts: int) -> bool:
        suffix_length = get_suffix_length(parts)
        total_length = 0
        for i in range(1, parts + 1):
            part_length = limit - len(f""<{i}/{parts}>"")
            if part_length <= 0:
                return False
            total_length += part_length
        return total_length >= len(message)
    
    def precompute_suffix_lengths(max_parts: int) -> List[int]:
        return [len(f""<{i}/{max_parts}>"") for i in range(1, max_parts + 1)]
    
    n = len(message)
    
    if limit <= get_suffix_length(1):
        return []
    
    for parts in range(1, n + 1):
        if can_split_into_parts(parts):
            result = []
            index = 0
            suffix_lengths = precompute_suffix_lengths(parts)
            for i in range(1, parts + 1):
                suffix = f""<{i}/{parts}>""
                part_length = limit - suffix_lengths[i - 1]
                part = message[index:index + part_length]
                result.append(part + suffix)
                index += part_length
            return result
    
    return []",55,False,55,94,,"**Tests that the code passed:**

assert splitMessage(""this is really a very awesome message"", 9) == [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""]
assert splitMessage(""short message"", 15) == [""short mess<1/2>"",""age<2/2>""]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
60,maximum-number-of-non-overlapping-palindrome-substrings,"
def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    dp = [0] * (n + 1)

    for i in range(n - k + 1):
        for j in range(i + k, n + 1):
            if is_palindrome(s[i:j]):
                dp[j] = max(dp[j], dp[i] + 1)

    return max(dp)
",55,False,12,56,,"**Tests that the code passed:**

assert maxPalindromes(""adbcda"", 2) == 0

**Tests that the code failed:**

assert maxPalindromes(""abaccdbbd"", 3) == 2 # ERROR: This unit test fails. Output was 1, but expected value was: 2","(False, True)",0
61,maximum-number-of-non-overlapping-palindrome-substrings,"def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    if k > n:
        return 0

    # Precompute palindrome table
    palindrome = [[False] * n for _ in range(n)]
    for i in range(n):
        palindrome[i][i] = True
    for i in range(n - 1):
        palindrome[i][i + 1] = (s[i] == s[i + 1])
    for length in range(3, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            palindrome[i][j] = (s[i] == s[j] and palindrome[i + 1][j - 1])

    dp = [0] * (n + 1)  # dp[i] will store the maximum number of palindromic substrings ending at or before i

    for i in range(n):
        dp[i + 1] = dp[i]  # carry forward the previous value
        for j in range(i + 1):
            if i - j + 1 >= k and palindrome[j][i]:
                dp[i + 1] = max(dp[i + 1], dp[j] + 1)

    return dp[n]",55,False,0,56,,"**Tests that the code passed:**

assert maxPalindromes(""abaccdbbd"", 3) == 2
assert maxPalindromes(""adbcda"", 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
62,minimum-total-distance-traveled,"
from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort()
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for i in range(1, n + 1):
        for j in range(1, m + 1):
            dp[i][j] = dp[i][j - 1]
            total_distance = 0
            for k in range(1, min(factory[j - 1][1], i) + 1):
                total_distance += abs(robot[i - k] - factory[j - 1][0])
                dp[i][j] = min(dp[i][j], dp[i - k][j - 1] + total_distance)
    
    return dp[n][m]
",55,False,0,40,,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
63,minimum-total-distance-traveled,"from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort()
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for i in range(1, n + 1):
        for j in range(1, m + 1):
            dp[i][j] = dp[i][j - 1]
            total_distance = 0
            for k in range(1, min(factory[j - 1][1], i) + 1):
                if robot[i - k] == factory[j - 1][0]:
                    total_distance = 0
                else:
                    total_distance += abs(robot[i - k] - factory[j - 1][0])
                dp[i][j] = min(dp[i][j], dp[i - k][j - 1] + total_distance)
    
    return dp[n][m]",55,False,0,40,,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",1
64,next-greater-element-iv,"
from typing import List

def secondGreaterElement(nums: List[int]) -> List[int]:
    n = len(nums)
    answer = [-1] * n
    stack1 = []
    stack2 = []
    
    for i in range(n):
        while stack2 and nums[stack2[-1]] < nums[i]:
            answer[stack2.pop()] = nums[i]
        temp = []
        while stack1 and nums[stack1[-1]] < nums[i]:
            temp.append(stack1.pop())
        stack2.extend(temp)
        stack1.append(i)
    
    return answer
",55,False,19,51,,"**Tests that the code passed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1]
assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
65,next-greater-element-iv,"from typing import List
from collections import deque

def secondGreaterElement(nums: List[int]) -> List[int]:
    n = len(nums)
    answer = [-1] * n
    stack1 = []
    stack2 = deque()
    
    for i in range(n):
        while stack2 and nums[stack2[-1]] < nums[i]:
            answer[stack2.pop()] = nums[i]
        temp = []
        while stack1 and nums[stack1[-1]] < nums[i]:
            temp.append(stack1.pop())
        stack2.extend(temp)
        stack1.append(i)
    
    return answer",55,False,-1,-1,-1,"**Tests that the code passed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1]
assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
66,minimum-number-of-operations-to-make-arrays-similar,"
from typing import List

def makeSimilar(nums: List[int], target: List[int]) -> int:
    nums.sort()
    target.sort()
    
    odd_nums = [x for x in nums if x % 2 != 0]
    even_nums = [x for x in nums if x % 2 == 0]
    odd_target = [x for x in target if x % 2 != 0]
    even_target = [x for x in target if x % 2 == 0]
    
    def count_operations(source: List[int], target: List[int]) -> int:
        operations = 0
        for s, t in zip(source, target):
            if s < t:
                operations += (t - s) // 2
        return operations
    
    return count_operations(odd_nums, odd_target) + count_operations(even_nums, even_target)
",55,True,32,32,701 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
67,minimum-number-of-operations-to-make-arrays-similar,"from typing import List

def makeSimilar(nums: List[int], target: List[int]) -> int:
    # Validate input constraints
    assert len(nums) == len(target), ""The lengths of nums and target must be equal""
    assert all(1 <= x <= 10**6 for x in nums), ""Elements of nums must be between 1 and 10^6""
    assert all(1 <= x <= 10**6 for x in target), ""Elements of target must be between 1 and 10^6""
    
    # Sort and separate odd and even numbers in a single pass
    nums.sort()
    target.sort()
    
    odd_nums, even_nums = [], []
    odd_target, even_target = [], []
    
    for x in nums:
        if x % 2 == 0:
            even_nums.append(x)
        else:
            odd_nums.append(x)
    
    for x in target:
        if x % 2 == 0:
            even_target.append(x)
        else:
            odd_target.append(x)
    
    def count_operations(source: List[int], target: List[int]) -> int:
        operations = 0
        for s, t in zip(source, target):
            if s < t:
                operations += (t - s) // 2
        return operations
    
    return count_operations(odd_nums, odd_target) + count_operations(even_nums, even_target)",55,True,32,32,709 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
68,minimum-cost-to-make-array-equal,"
from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    left, right = min(nums), max(nums)
    while left < right:
        mid = (left + right) // 2
        cost1 = get_cost(mid)
        cost2 = get_cost(mid + 1)
        
        if cost1 < cost2:
            right = mid
        else:
            left = mid + 1
    
    return get_cost(left)
",55,True,48,48,595 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
69,minimum-cost-to-make-array-equal,"from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    # Edge case: if all elements in nums are the same, return 0
    if len(set(nums)) == 1:
        return 0
    
    left, right = min(nums), max(nums)
    cache = {}
    
    while left < right:
        mid = (left + right) // 2
        if mid not in cache:
            cache[mid] = get_cost(mid)
        if mid + 1 not in cache:
            cache[mid + 1] = get_cost(mid + 1)
        
        cost1 = cache[mid]
        cost2 = cache[mid + 1]
        
        if cost1 < cost2:
            right = mid
        else:
            left = mid + 1
    
    return cache[left]",55,True,48,48,576 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
70,create-components-with-same-value,"
from typing import List
from collections import defaultdict, deque

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = [False] * n
        def dfs(node):
            visited[node] = True
            total = nums[node]
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total > target:
                return -1
            return total if total < target else 0
        
        for i in range(n):
            if not visited[i]:
                if dfs(i) != 0:
                    return False
        return True

    n = len(nums)
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    for k in range(n, 0, -1):
        if total_sum % k == 0:
            if can_split(total_sum // k):
                return k - 1
    return 0
",55,True,43,43,1162 ms,"**Tests that the code passed:**

assert componentValue([6,2,2,2,6], [[0,1],[1,2],[1,3],[3,4]]) == 2
assert componentValue([2], []) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
71,create-components-with-same-value,"from typing import List
from collections import defaultdict, deque

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = [False] * n
        memo = {}

        def dfs(node):
            if node in memo:
                return memo[node]
            visited[node] = True
            total = nums[node]
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total > target:
                memo[node] = -1
                return -1
            memo[node] = total if total < target else 0
            return memo[node]
        
        for i in range(n):
            if not visited[i]:
                if dfs(i) != 0:
                    return False
        return True

    n = len(nums)
    if n == 1:
        return 0

    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    if total_sum == 0:
        return n - 1

    for k in range(n, 0, -1):
        if total_sum % k == 0:
            if can_split(total_sum // k):
                return k - 1
    return 0",55,True,43,43,1197 ms,"**Tests that the code passed:**

assert componentValue([6,2,2,2,6], [[0,1],[1,2],[1,3],[3,4]]) == 2
assert componentValue([2], []) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
72,count-subarrays-with-fixed-bounds,"
def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = left_bound = -1

    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            left_bound = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        count += max(0, min(min_pos, max_pos) - left_bound)
    
    return count
",55,True,52,52,703 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",0
73,count-subarrays-with-fixed-bounds,"from typing import List

def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = left_bound = -1

    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            left_bound = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        count += max(0, min(min_pos, max_pos) - left_bound)
    
    return count",55,True,52,52,703 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",1
74,longest-increasing-subsequence-ii,"
from typing import List
from bisect import bisect_left

def lengthOfLIS(nums: List[int], k: int) -> int:
    dp = []
    
    for num in nums:
        pos = bisect_left(dp, num)
        if pos < len(dp):
            if pos == 0 or num - dp[pos - 1] <= k:
                dp[pos] = num
        else:
            if pos == 0 or num - dp[-1] <= k:
                dp.append(num)
    
    return len(dp)
",55,False,76,84,,"**Tests that the code passed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4
assert lengthOfLIS([1,5], 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
75,longest-increasing-subsequence-ii,"from typing import List
from bisect import bisect_left

def lengthOfLIS(nums: List[int], k: int) -> int:
    if not nums:
        return 0
    
    dp = []
    
    for num in nums:
        pos = bisect_left(dp, num)
        if pos < len(dp):
            if pos == 0 or num - dp[pos - 1] <= k:
                dp[pos] = num
        else:
            if pos == 0 or num - dp[-1] <= k:
                dp.append(num)
    
    return len(dp)",55,False,76,84,,"**Tests that the code passed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4
assert lengthOfLIS([1,5], 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
76,paths-in-matrix-whose-sum-is-divisible-by-k,"
from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # dp[i][j][r] will store the number of paths to (i, j) with sum % k == r
    dp = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp[i][j][r] > 0:
                    if i + 1 < m:
                        dp[i + 1][j][(r + grid[i + 1][j]) % k] = (dp[i + 1][j][(r + grid[i + 1][j]) % k] + dp[i][j][r]) % MOD
                    if j + 1 < n:
                        dp[i][j + 1][(r + grid[i][j + 1]) % k] = (dp[i][j + 1][(r + grid[i][j + 1]) % k] + dp[i][j][r]) % MOD
    
    return dp[m - 1][n - 1][0]
",55,True,88,88,2544 ms,"**Tests that the code passed:**

assert numberOfPaths([[5,2,4],[3,0,5],[0,7,2]], 3) == 2
assert numberOfPaths([[0,0]], 5) == 1
assert numberOfPaths([[7,3,4,9],[2,3,6,2],[2,3,7,0]], 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
77,paths-in-matrix-whose-sum-is-divisible-by-k,"from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # Initialize dp array with a full 3D array to ensure correctness
    dp = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp[i][j][r] > 0:
                    if i + 1 < m:
                        dp[i + 1][j][(r + grid[i + 1][j]) % k] = (dp[i + 1][j][(r + grid[i + 1][j]) % k] + dp[i][j][r]) % MOD
                    if j + 1 < n:
                        dp[i][j + 1][(r + grid[i][j + 1]) % k] = (dp[i][j + 1][(r + grid[i][j + 1]) % k] + dp[i][j][r]) % MOD
    
    return dp[m - 1][n - 1][0] % MOD",55,True,88,88,2632 ms,"**Tests that the code passed:**

assert numberOfPaths([[5,2,4],[3,0,5],[0,7,2]], 3) == 2
assert numberOfPaths([[0,0]], 5) == 1
assert numberOfPaths([[7,3,4,9],[2,3,6,2],[2,3,7,0]], 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
78,minimum-time-to-visit-a-cell-in-a-grid,"
from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    visited = set()

    while pq:
        time, row, col = heapq.heappop(pq)
        if (row, col) in visited:
            continue
        visited.add((row, col))
        if row == m - 1 and col == n - 1:
            return time
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n and (new_row, new_col) not in visited:
                new_time = max(time + 1, grid[new_row][new_col])
                heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1
",91,False,22,41,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minimumTime([[0,1,3,2],[5,1,2,5],[4,3,8,6]]) == 7 # ERROR: This unit test fails. Output was 6, but expected value was: 7
assert minimumTime([[0,2,4],[3,2,1],[1,0,4]]) == -1 # ERROR: This unit test fails. Output was 5, but expected value was: -1","(False, False)",0
79,minimum-time-to-visit-a-cell-in-a-grid,"from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    min_time = [[float('inf')] * n for _ in range(m)]
    min_time[0][0] = 0

    while pq:
        time, row, col = heapq.heappop(pq)
        if row == m - 1 and col == n - 1:
            return time
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n:
                new_time = max(time + 1, grid[new_row][new_col])
                if new_time < min_time[new_row][new_col]:
                    min_time[new_row][new_col] = new_time
                    heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1",91,False,22,41,,,,1
80,find-the-string-with-lcp,"
from typing import List

def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    word = [''] * n
    current_char = 'a'
    
    for i in range(n):
        if word[i] == '':
            word[i] = current_char
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    word[j] = word[i]
            current_char = chr(ord(current_char) + 1)
    
    for i in range(n):
        for j in range(n):
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    return ''.join(word)
",91,False,36,55,,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
81,find-the-string-with-lcp,"from typing import List

def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    word = [''] * n
    current_char = 'a'
    
    for i in range(n):
        if word[i] == '':
            word[i] = current_char
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    if word[j] == '':
                        word[j] = word[i]
                    elif word[j] != word[i]:
                        return """"
            current_char = chr(ord(current_char) + 1)
    
    for i in range(n):
        for j in range(n):
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    return ''.join(word)",91,False,36,55,,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
82,handling-sum-queries-after-update,"
from typing import List

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    result = []
    
    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            for i in range(l, r + 1):
                nums1[i] = 1 - nums1[i]
        elif query[0] == 2:
            p = query[1]
            for i in range(len(nums1)):
                nums2[i] += nums1[i] * p
        elif query[0] == 3:
            result.append(sum(nums2))
    
    return result
",91,False,72,77,,"**Tests that the code passed:**

assert handleQuery([1,0,1], [0,0,0], [[1,1,1],[2,1,0],[3,0,0]]) == [3]
assert handleQuery([1], [5], [[2,0,0],[3,0,0]]) == [5]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
83,handling-sum-queries-after-update,"from typing import List

class SegmentTree:
    def __init__(self, n):
        self.n = n
        self.tree = [0] * (4 * n)
        self.lazy = [0] * (4 * n)

    def update_range(self, start, end, l, r, node):
        if self.lazy[node] != 0:
            self.tree[node] = (r - l + 1) - self.tree[node]
            if l != r:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > r or end < l:
            return

        if start <= l and r <= end:
            self.tree[node] = (r - l + 1) - self.tree[node]
            if l != r:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            return

        mid = (l + r) // 2
        self.update_range(start, end, l, mid, node * 2 + 1)
        self.update_range(start, end, mid + 1, r, node * 2 + 2)
        self.tree[node] = self.tree[node * 2 + 1] + self.tree[node * 2 + 2]

    def query_range(self, start, end, l, r, node):
        if self.lazy[node] != 0:
            self.tree[node] = (r - l + 1) - self.tree[node]
            if l != r:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > r or end < l:
            return 0

        if start <= l and r <= end:
            return self.tree[node]

        mid = (l + r) // 2
        left_query = self.query_range(start, end, l, mid, node * 2 + 1)
        right_query = self.query_range(start, end, mid + 1, r, node * 2 + 2)
        return left_query + right_query

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    n = len(nums1)
    result = []
    seg_tree = SegmentTree(n)
    
    for i in range(n):
        if nums1[i] == 1:
            seg_tree.update_range(i, i, 0, n - 1, 0)
    
    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            seg_tree.update_range(l, r, 0, n - 1, 0)
        elif query[0] == 2:
            p = query[1]
            for i in range(n):
                if seg_tree.query_range(i, i, 0, n - 1, 0) == 1:
                    nums2[i] += p
        elif query[0] == 3:
            result.append(sum(nums2))
    
    return result",91,False,0,77,,"**Tests that the code passed:**

assert handleQuery([1,0,1], [0,0,0], [[1,1,1],[2,1,0],[3,0,0]]) == [3]
assert handleQuery([1], [5], [[2,0,0],[3,0,0]]) == [5]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
84,subsequence-with-the-minimum-score,"
def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Initialize the left and right pointers for the two-pointer technique
    left, right = 0, m - 1
    
    # Create an array to store the minimum index in s where t[:i+1] is a subsequence
    left_indices = [-1] * m
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            left_indices[j] = i
            j += 1
    
    # Create an array to store the maximum index in s where t[i:] is a subsequence
    right_indices = [-1] * m
    j = m - 1
    for i in range(n - 1, -1, -1):
        if j >= 0 and s[i] == t[j]:
            right_indices[j] = i
            j -= 1
    
    # If t is already a subsequence of s, return 0
    if left_indices[m - 1] != -1:
        return 0
    
    # Initialize the minimum score to the length of t
    min_score = m
    
    # Check the minimum score by removing characters from the beginning of t
    for i in range(m):
        if left_indices[i] != -1:
            min_score = min(min_score, m - 1 - i)
    
    # Check the minimum score by removing characters from the end of t
    for i in range(m - 1, -1, -1):
        if right_indices[i] != -1:
            min_score = min(min_score, i)
    
    # Check the minimum score by removing characters from the middle of t
    for i in range(m - 1):
        if left_indices[i] != -1 and right_indices[i + 1] != -1:
            min_score = min(min_score, right_indices[i + 1] - left_indices[i] - 1)
    
    return min_score
",91,False,28,54,,"**Tests that the code passed:**

assert minimumScore(""cde"", ""xyz"") == 3

**Tests that the code failed:**

assert minimumScore(""abacaba"", ""bzaa"") == 1 # ERROR: This unit test fails. Output was 2, but expected value was: 1","(False, True)",0
85,subsequence-with-the-minimum-score,"def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Initialize the left and right pointers for the two-pointer technique
    left, right = 0, m - 1
    
    # Create an array to store the minimum index in s where t[:i+1] is a subsequence
    left_indices = [-1] * m
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            left_indices[j] = i
            j += 1
    
    # Create an array to store the maximum index in s where t[i:] is a subsequence
    right_indices = [-1] * m
    j = m - 1
    for i in range(n - 1, -1, -1):
        if j >= 0 and s[i] == t[j]:
            right_indices[j] = i
            j -= 1
    
    # If t is already a subsequence of s, return 0
    if left_indices[m - 1] != -1:
        return 0
    
    # Initialize the minimum score to the length of t
    min_score = m
    
    # Check the minimum score by removing characters from the beginning of t
    for i in range(m):
        if left_indices[i] != -1:
            min_score = min(min_score, m - 1 - i)
    
    # Check the minimum score by removing characters from the end of t
    for i in range(m - 1, -1, -1):
        if right_indices[i] != -1:
            min_score = min(min_score, i)
    
    # Check the minimum score by removing characters from the middle of t
    for i in range(m):
        for j in range(i, m):
            if left_indices[i] != -1 and right_indices[j] != -1 and left_indices[i] < right_indices[j]:
                min_score = min(min_score, j - i - 1)
    
    return min_score",91,False,45,54,,"**Tests that the code passed:**

assert minimumScore(""abacaba"", ""bzaa"") == 1
assert minimumScore(""cde"", ""xyz"") == 3

**Tests that the code failed:**

No tests failed.
","(True, True)",1
86,minimum-number-of-visited-cells-in-a-grid,"
from typing import List
from collections import deque

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    if m == 1 and n == 1:
        return 1

    directions = [(0, 1), (1, 0)]
    queue = deque([(0, 0, 1)])  # (row, col, steps)
    visited = set((0, 0))

    while queue:
        i, j, steps = queue.popleft()
        
        for di, dj in directions:
            for k in range(1, grid[i][j] + 1):
                ni, nj = i + k * di, j + k * dj
                if 0 <= ni < m and 0 <= nj < n and (ni, nj) not in visited:
                    if ni == m - 1 and nj == n - 1:
                        return steps + 1
                    queue.append((ni, nj, steps + 1))
                    visited.add((ni, nj))
    
    return -1
",91,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
87,minimum-number-of-visited-cells-in-a-grid,"from typing import List
from collections import deque

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    if m == 1 and n == 1:
        return 1 if grid[0][0] != 0 else -1

    directions = [(0, 1), (1, 0)]
    queue = deque([(0, 0, 1)])  # (row, col, steps)
    visited = set()
    visited.add((0, 0))

    while queue:
        i, j, steps = queue.popleft()
        
        for di, dj in directions:
            if di == 0:  # Rightward movement
                for nj in range(j + 1, min(j + grid[i][j] + 1, n)):
                    if (i, nj) not in visited:
                        if i == m - 1 and nj == n - 1:
                            return steps + 1
                        queue.append((i, nj, steps + 1))
                        visited.add((i, nj))
            else:  # Downward movement
                for ni in range(i + 1, min(i + grid[i][j] + 1, m)):
                    if (ni, j) not in visited:
                        if ni == m - 1 and j == n - 1:
                            return steps + 1
                        queue.append((ni, j, steps + 1))
                        visited.add((ni, j))
    
    return -1",91,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
88,rearranging-fruits,"
from typing import List
from collections import Counter

def minCost(basket1: List[int], basket2: List[int]) -> int:
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(basket1 + basket2):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the excess fruits in each basket
    excess1 = []
    excess2 = []
    for fruit in count1:
        if count1[fruit] > count2[fruit]:
            excess1.extend([fruit] * ((count1[fruit] - count2[fruit]) // 2))
    for fruit in count2:
        if count2[fruit] > count1[fruit]:
            excess2.extend([fruit] * ((count2[fruit] - count1[fruit]) // 2))
    
    # Sort the excess fruits to minimize the cost
    excess1.sort()
    excess2.sort(reverse=True)
    
    # Calculate the minimum cost
    min_cost = 0
    for i in range(len(excess1)):
        min_cost += min(excess1[i], excess2[i])
    
    return min_cost
",91,False,20,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
89,rearranging-fruits,"from typing import List
from collections import Counter
import heapq

def minCost(basket1: List[int], basket2: List[int]) -> int:
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(basket1 + basket2):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the excess fruits in each basket
    excess1 = []
    excess2 = []
    for fruit in count1:
        if count1[fruit] > count2[fruit]:
            excess1.extend([fruit] * ((count1[fruit] - count2[fruit]) // 2))
    for fruit in count2:
        if count2[fruit] > count1[fruit]:
            excess2.extend([fruit] * ((count2[fruit] - count1[fruit]) // 2))
    
    # Use a min-heap to always get the smallest available fruit
    heapq.heapify(excess1)
    heapq.heapify(excess2)
    
    # Calculate the minimum cost
    min_cost = 0
    while excess1 and excess2:
        min_cost += min(heapq.heappop(excess1), heapq.heappop(excess2))
    
    return min_cost",91,False,19,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
90,lexicographically-smallest-beautiful-string,"
def smallestBeautifulString(s: str, k: int) -> str:
    def is_beautiful(s: str) -> bool:
        for i in range(len(s) - 1):
            if s[i] == s[i + 1]:
                return False
        for i in range(len(s) - 2):
            if s[i] == s[i + 2]:
                return False
        return True

    n = len(s)
    s = list(s)
    for i in range(n - 1, -1, -1):
        for j in range(ord(s[i]) - ord('a') + 1, k):
            s[i] = chr(ord('a') + j)
            if is_beautiful(s[:i + 1]):
                for l in range(i + 1, n):
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s[:l + 1]):
                            break
                return ''.join(s)
        s[i] = 'a'
    return """"
",91,False,1042,1054,,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",0
91,lexicographically-smallest-beautiful-string,"def smallestBeautifulString(s: str, k: int) -> str:
    def next_char(c, k):
        return chr((ord(c) - ord('a') + 1) % k + ord('a'))

    def is_beautiful(s: str) -> bool:
        for i in range(len(s) - 1):
            if s[i] == s[i + 1]:
                return False
        for i in range(len(s) - 2):
            if s[i] == s[i + 2]:
                return False
        return True

    n = len(s)
    s = list(s)
    for i in range(n - 1, -1, -1):
        original_char = s[i]
        for j in range(1, k):
            s[i] = next_char(s[i], k)
            if s[i] <= original_char:
                continue
            if is_beautiful(s[:i + 1]):
                for l in range(i + 1, n):
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s[:l + 1]):
                            break
                return ''.join(s)
        s[i] = 'a'
    return """"",91,False,1042,1054,,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",1
92,count-increasing-quadruplets,"
def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    for i in range(n):
        for j in range(i + 1, n):
            for k in range(i + 1, j):
                for l in range(j + 1, n):
                    if nums[i] < nums[k] < nums[j] < nums[l]:
                        count += 1
                        
    return count
",91,False,11,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2

**Tests that the code failed:**

assert countQuadruplets([1,2,3,4]) == 0 # ERROR: This unit test fails. Output was 1, but expected value was: 0","(True, False)",0
93,count-increasing-quadruplets,"from typing import List

def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    for i in range(n):
        for j in range(i + 1, n):
            for k in range(j + 1, n):
                for l in range(k + 1, n):
                    if nums[i] < nums[k] < nums[j] < nums[l]:
                        count += 1
                        
    return count",91,False,63,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2
assert countQuadruplets([1,2,3,4]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
94,put-marbles-in-bags,"
from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    if k == 1:
        return 0

    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the minimum and maximum possible scores
    costs.sort()
    
    # The minimum score is the sum of the smallest k-1 costs
    min_score = sum(costs[:k-1])
    
    # The maximum score is the sum of the largest k-1 costs
    max_score = sum(costs[-(k-1):])
    
    return max_score - min_score
",91,True,103,103,552 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
95,put-marbles-in-bags,"from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    
    # Early exit for trivial cases
    if k == 1 or k == n:
        return 0
    
    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the minimum and maximum possible scores
    costs.sort()
    
    # The minimum score is the sum of the smallest k-1 costs
    min_score = sum(costs[:k-1])
    
    # The maximum score is the sum of the largest k-1 costs
    max_score = sum(costs[-(k-1):])
    
    return max_score - min_score",91,True,103,103,549 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
96,shortest-cycle-in-a-graph,"
from typing import List
from collections import deque, defaultdict

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    def bfs(start):
        dist = [-1] * n
        dist[start] = 0
        q = deque([(start, -1)])
        while q:
            node, parent = q.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    q.append((neighbor, node))
                elif neighbor != parent:
                    return dist[node] + dist[neighbor] + 1
        return float('inf')
    
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i))
    
    return shortest_cycle if shortest_cycle != float('inf') else -1
",91,False,84,88,,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
97,shortest-cycle-in-a-graph,"from typing import List
from collections import deque, defaultdict
import heapq

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    def bfs(start):
        dist = [-1] * n
        dist[start] = 0
        q = deque([(start, -1)])
        shortest_cycle = float('inf')
        while q:
            node, parent = q.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    q.append((neighbor, node))
                elif neighbor != parent:
                    shortest_cycle = min(shortest_cycle, dist[node] + dist[neighbor] + 1)
                    if shortest_cycle == 3:  # Early termination for the smallest possible cycle
                        return shortest_cycle
        return shortest_cycle
    
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i))
    
    return shortest_cycle if shortest_cycle != float('inf') else -1",91,True,88,88,1426 ms,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
98,minimum-time-to-complete-all-tasks,"
def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = 0
    current_time = 0

    for start, end, duration in tasks:
        if current_time < start:
            current_time = start
        if current_time + duration <= end:
            time_on += duration
            current_time += duration
        else:
            time_on += end - current_time + 1
            current_time = end + 1

    return time_on
",91,False,275,1059,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert findMinimumTime([[2,3,1],[4,5,1],[1,5,2]]) == 2 # ERROR: This unit test fails. Output was 3, but expected value was: 2
assert findMinimumTime([[1,3,2],[2,5,3],[5,6,2]]) == 4 # ERROR: This unit test fails. Output was 6, but expected value was: 4","(False, False)",0
99,minimum-time-to-complete-all-tasks,"from typing import List
import heapq

def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = 0
    used_slots = set()

    for start, end, duration in tasks:
        available_slots = [t for t in range(start, end + 1) if t not in used_slots]
        
        if len(available_slots) >= duration:
            for t in available_slots[:duration]:
                used_slots.add(t)
        else:
            for t in available_slots:
                used_slots.add(t)
            remaining_duration = duration - len(available_slots)
            for t in range(end + 1, end + 1 + remaining_duration):
                used_slots.add(t)

    time_on = len(used_slots)
    return time_on

# Improved version with better handling of overlapping tasks and optimized slot allocation
def findMinimumTimeOptimized(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = 0
    used_slots = set()
    min_heap = []

    for start, end, duration in tasks:
        while min_heap and min_heap[0] < start:
            heapq.heappop(min_heap)
        
        available_slots = [t for t in range(start, end + 1) if t not in used_slots]
        
        if len(available_slots) >= duration:
            for t in available_slots[:duration]:
                used_slots.add(t)
                heapq.heappush(min_heap, t)
        else:
            for t in available_slots:
                used_slots.add(t)
                heapq.heappush(min_heap, t)
            remaining_duration = duration - len(available_slots)
            for t in range(end + 1, end + 1 + remaining_duration):
                used_slots.add(t)
                heapq.heappush(min_heap, t)

    time_on = len(used_slots)
    return time_on",91,False,259,1059,,,,1
100,count-number-of-possible-root-nodes,"
from collections import defaultdict, deque
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node)

    def count_correct_guesses(root):
        correct_count = 0
        for u, v in guesses:
            if parent_map[v] == u:
                correct_count += 1
        return correct_count

    n = len(edges) + 1
    tree = build_tree(edges)
    parent_map = [-1] * n

    # Choose an arbitrary node as the root and build the parent map
    dfs(0, -1)

    # Count the number of correct guesses for each possible root
    possible_roots = 0
    for root in range(n):
        correct_guesses = count_correct_guesses(root)
        if correct_guesses >= k:
            possible_roots += 1

    return possible_roots
",91,False,35,51,,"**Tests that the code passed:**

assert rootCount([[0,1],[1,2],[2,3],[3,4]], [[1,0],[3,4],[2,1],[3,2]], 1) == 5

**Tests that the code failed:**

assert rootCount([[0,1],[1,2],[1,3],[4,2]], [[1,3],[0,1],[1,0],[2,4]], 3) == 3 # ERROR: This unit test fails. Output was 5, but expected value was: 3","(False, True)",0
101,count-number-of-possible-root-nodes,"from collections import defaultdict, deque
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node)

    def count_correct_guesses(root):
        correct_count = 0
        for u, v in guesses:
            if parent_map[v] == u:
                correct_count += 1
        return correct_count

    def reroot_and_count_correct_guesses(node, parent, correct_guesses):
        nonlocal possible_roots
        if correct_guesses >= k:
            possible_roots += 1
        for neighbor in tree[node]:
            if neighbor != parent:
                new_correct_guesses = correct_guesses
                if (node, neighbor) in guess_set:
                    new_correct_guesses -= 1
                if (neighbor, node) in guess_set:
                    new_correct_guesses += 1
                reroot_and_count_correct_guesses(neighbor, node, new_correct_guesses)

    n = len(edges) + 1
    tree = build_tree(edges)
    parent_map = [-1] * n

    # Choose an arbitrary node as the root and build the parent map
    dfs(0, -1)

    # Precompute the number of correct guesses for the initial root
    guess_set = set((u, v) for u, v in guesses)
    initial_correct_guesses = count_correct_guesses(0)

    # Use rerooting technique to count correct guesses for each possible root
    possible_roots = 0
    reroot_and_count_correct_guesses(0, -1, initial_correct_guesses)

    return possible_roots",91,True,51,51,1594 ms,"**Tests that the code passed:**

assert rootCount([[0,1],[1,2],[1,3],[4,2]], [[1,3],[0,1],[1,0],[2,4]], 3) == 3
assert rootCount([[0,1],[1,2],[2,3],[3,4]], [[1,0],[3,4],[2,1],[3,2]], 1) == 5

**Tests that the code failed:**

No tests failed.
","(True, True)",1
102,number-of-ways-to-earn-points,"
from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] means the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # We need to update dp array for each type of question
        new_dp = dp[:]
        for t in range(target + 1):
            for k in range(1, count + 1):
                if t + k * marks <= target:
                    new_dp[t + k * marks] = (new_dp[t + k * marks] + dp[t]) % MOD
                else:
                    break
        dp = new_dp
    
    return dp[target]
",91,True,63,63,1417 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
103,number-of-ways-to-earn-points,"from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] means the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # Update dp array in place to avoid extra space usage
        for current_score in range(target, -1, -1):
            for question_count in range(1, count + 1):
                new_score = current_score + question_count * marks
                if new_score > target:
                    break
                dp[new_score] = (dp[new_score] + dp[current_score]) % MOD
    
    return dp[target]",91,True,63,63,1078 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
104,split-the-array-to-make-coprime-products,"
from typing import List
from math import gcd
from functools import reduce

def findValidSplit(nums: List[int]) -> int:
    def product(arr):
        return reduce(lambda x, y: x * y, arr, 1)
    
    n = nums.length
    for i in range(n - 1):
        left_product = product(nums[:i + 1])
        right_product = product(nums[i + 1:])
        if gcd(left_product, right_product) == 1:
            return i
    return -1
",91,False,29,75,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert findValidSplit([4,7,8,15,3,5]) == 2 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.
assert findValidSplit([4,7,15,8,3,5]) == -1 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.","(False, False)",0
105,split-the-array-to-make-coprime-products,"from typing import List
from math import gcd

def findValidSplit(nums: List[int]) -> int:
    n = len(nums)
    left_product = 1
    right_product = 1
    total_product = 1
    
    for num in nums:
        total_product *= num
    
    for i in range(n - 1):
        left_product *= nums[i]
        right_product = total_product // left_product
        if gcd(left_product, right_product) == 1:
            return i
    return -1",91,False,29,75,,"**Tests that the code passed:**

assert findValidSplit([4,7,8,15,3,5]) == 2
assert findValidSplit([4,7,15,8,3,5]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
106,time-to-cross-a-bridge,"
from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = [(time[i][0] + time[i][2], i, time[i]) for i in range(k)]
    workers.sort()
    
    left_waiting = [(0, i) for _, i, _ in workers]
    right_waiting = []
    left_working = []
    right_working = []
    
    current_time = 0
    boxes_moved = 0
    
    while boxes_moved < n:
        if left_working and left_working[0][0] <= current_time:
            _, i = heapq.heappop(left_working)
            heapq.heappush(left_waiting, (current_time, i))
        
        if right_working and right_working[0][0] <= current_time:
            _, i = heapq.heappop(right_working)
            heapq.heappush(right_waiting, (current_time, i))
        
        if right_waiting:
            _, i = heapq.heappop(right_waiting)
            current_time += workers[i][2][2]
            heapq.heappush(left_working, (current_time + workers[i][2][3], i))
            boxes_moved += 1
        elif left_waiting and boxes_moved < n:
            _, i = heapq.heappop(left_waiting)
            current_time += workers[i][2][0]
            heapq.heappush(right_working, (current_time + workers[i][2][1], i))
        else:
            if left_working:
                current_time = left_working[0][0]
            elif right_working:
                current_time = right_working[0][0]
    
    return current_time
",91,False,0,65,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert findCrossingTime(1, 3, [[1,1,2,1],[1,1,3,1],[1,1,4,1]]) == 6 # ERROR: This unit test fails. Output was 4, but expected value was: 6
assert findCrossingTime(3, 2, [[1,9,1,8],[10,10,10,10]]) == 50 # ERROR: This unit test fails. Output was 32, but expected value was: 50","(False, False)",0
107,time-to-cross-a-bridge,"from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = [(time[i][0] + time[i][2], i, time[i]) for i in range(k)]
    workers.sort()
    
    left_waiting = [(0, i) for _, i, _ in workers]
    right_waiting = []
    left_working = []
    right_working = []
    
    current_time = 0
    boxes_moved = 0
    
    while boxes_moved < n:
        # Move workers from left_working to left_waiting if they have finished putting boxes
        while left_working and left_working[0][0] <= current_time:
            _, i = heapq.heappop(left_working)
            heapq.heappush(left_waiting, (current_time, i))
        
        # Move workers from right_working to right_waiting if they have finished picking boxes
        while right_working and right_working[0][0] <= current_time:
            _, i = heapq.heappop(right_working)
            heapq.heappush(right_waiting, (current_time, i))
        
        if right_waiting:
            # Worker crosses from right to left
            _, i = heapq.heappop(right_waiting)
            current_time += workers[i][2][2]
            heapq.heappush(left_working, (current_time + workers[i][2][3], i))
            boxes_moved += 1
        elif left_waiting and boxes_moved < n:
            # Worker crosses from left to right
            _, i = heapq.heappop(left_waiting)
            current_time += workers[i][2][0]
            heapq.heappush(right_working, (current_time + workers[i][2][1], i))
        else:
            # Advance time to the next event
            next_event_time = float('inf')
            if left_working:
                next_event_time = min(next_event_time, left_working[0][0])
            if right_working:
                next_event_time = min(next_event_time, right_working[0][0])
            current_time = next_event_time
    
    return current_time",91,False,0,65,,,,1
108,check-if-point-is-reachable,"
def isReachable(targetX: int, targetY: int) -> bool:
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

    # Reduce targetX and targetY by removing all factors of 2
    while targetX % 2 == 0:
        targetX //= 2
    while targetY % 2 == 0:
        targetY //= 2

    # Check if the gcd of the reduced targetX and targetY is 1
    return gcd(targetX, targetY) == 1

# Example usage:
# print(isReachable(6, 9))  # Output: False
# print(isReachable(4, 7))  # Output: True
",91,True,60,60,31 ms,"**Tests that the code passed:**

assert isReachable(6, 9) == False
assert isReachable(4, 7) == True

**Tests that the code failed:**

No tests failed.
","(True, True)",0
109,check-if-point-is-reachable,"def isReachable(targetX: int, targetY: int) -> bool:
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

    # Special case: if either targetX or targetY is 1, check if the other is a power of 2
    if targetX == 1 or targetY == 1:
        return (targetX & (targetX - 1)) == 0 or (targetY & (targetY - 1)) == 0

    # Reduce targetX and targetY by removing all factors of 2
    while targetX % 2 == 0:
        targetX //= 2
    while targetY % 2 == 0:
        targetY //= 2

    # Check if the gcd of the reduced targetX and targetY is 1
    return gcd(targetX, targetY) == 1

# Example usage:
# print(isReachable(6, 9))  # Output: False
# print(isReachable(4, 7))  # Output: True",91,True,60,60,42 ms,"**Tests that the code passed:**

assert isReachable(6, 9) == False
assert isReachable(4, 7) == True

**Tests that the code failed:**

No tests failed.
","(True, True)",1
110,minimum-cost-to-split-an-array,"
from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    n = nums.length
    dp = [float('inf')] * (n + 1)
    dp[0] = 0
    
    for i in range(1, n + 1):
        count = defaultdict(int)
        trimmed_length = 0
        for j in range(i, 0, -1):
            count[nums[j - 1]] += 1
            if count[nums[j - 1]] == 2:
                trimmed_length += 2
            elif count[nums[j - 1]] > 2:
                trimmed_length += 1
            dp[i] = min(dp[i], dp[j - 1] + k + trimmed_length)
    
    return dp[n]
",91,False,0,59,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.
assert minCost([1,2,1,2,1], 2) == 6 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.
assert minCost([1,2,1,2,1], 5) == 10 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.","(False, False, False)",0
111,minimum-cost-to-split-an-array,"from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    n = len(nums)
    dp = [float('inf')] * (n + 1)
    dp[0] = 0
    
    for i in range(1, n + 1):
        count = defaultdict(int)
        trimmed_length = 0
        for j in range(i, 0, -1):
            count[nums[j - 1]] += 1
            if count[nums[j - 1]] == 2:
                trimmed_length += 2
            elif count[nums[j - 1]] > 2:
                trimmed_length += 1
            dp[i] = min(dp[i], dp[j - 1] + k + trimmed_length)
    
    return dp[n]",91,True,59,59,3286 ms,"**Tests that the code passed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8
assert minCost([1,2,1,2,1], 2) == 6
assert minCost([1,2,1,2,1], 5) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
112,difference-between-maximum-and-minimum-price-sum,"
from typing import List
from collections import defaultdict

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    def dfs(node, parent):
        max1, max2 = 0, 0
        for neighbor in graph[node]:
            if neighbor == parent:
                continue
            sub_max = dfs(neighbor, node)
            if sub_max > max1:
                max1, max2 = sub_max, max1
            elif sub_max > max2:
                max2 = sub_max
        max_path_sum[node] = max1 + price[node]
        return max_path_sum[node]

    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)

    max_path_sum = [0] * n
    dfs(0, -1)

    max_cost = 0
    for node in range(n):
        max_cost = max(max_cost, max_path_sum[node] - price[node])

    return max_cost
",91,False,12,58,,"**Tests that the code passed:**

assert maxOutput(6, [[0,1],[1,2],[1,3],[3,4],[3,5]], [9,8,7,6,10,5]) == 24
assert maxOutput(3, [[0,1],[1,2]], [1,1,1]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
113,difference-between-maximum-and-minimum-price-sum,"from typing import List
from collections import defaultdict

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    def dfs(node, parent):
        max_sum = price[node]
        min_sum = price[node]
        for neighbor in graph[node]:
            if neighbor == parent:
                continue
            sub_max, sub_min = dfs(neighbor, node)
            max_sum = max(max_sum, sub_max + price[node])
            min_sum = min(min_sum, sub_min + price[node])
        max_path_sum[node] = max_sum
        min_path_sum[node] = min_sum
        return max_sum, min_sum

    def calculate_cost(root):
        dfs(root, -1)
        max_cost = 0
        for node in range(n):
            max_cost = max(max_cost, max_path_sum[node] - min_path_sum[node])
        return max_cost

    def find_centroid(node, parent, size):
        is_centroid = True
        heaviest_child = 0
        subtree_size[node] = 1
        for neighbor in graph[node]:
            if neighbor == parent:
                continue
            find_centroid(neighbor, node, size)
            subtree_size[node] += subtree_size[neighbor]
            if subtree_size[neighbor] > size // 2:
                is_centroid = False
            if subtree_size[neighbor] > heaviest_child:
                heaviest_child = subtree_size[neighbor]
        if size - subtree_size[node] > size // 2:
            is_centroid = False
        if is_centroid:
            centroids.append(node)

    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)

    max_path_sum = [0] * n
    min_path_sum = [0] * n
    subtree_size = [0] * n
    centroids = []

    find_centroid(0, -1, n)
    max_cost = 0
    for centroid in centroids:
        max_cost = max(max_cost, calculate_cost(centroid))

    return max_cost",91,False,3,58,,,,1
114,maximize-the-minimum-powered-city,"
from typing import List

def maxPower(stations: List[int], r: int, k: int) -> int:
    def is_possible(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        
        for i in range(len(stations)):
            if i > r:
                current_power -= additional_stations[i - r - 1]
            
            if stations[i] + current_power < min_power:
                needed = min_power - (stations[i] + current_power)
                if total_additional + needed > k:
                    return False
                additional_stations[min(i + r, len(stations) - 1)] += needed
                current_power += needed
                total_additional += needed
        
        return True

    low, high = 0, max(stations) + k
    while low < high:
        mid = (low + high + 1) // 2
        if is_possible(mid):
            low = mid
        else:
            high = mid - 1
    
    return low
",91,False,9,30,,"**Tests that the code passed:**

assert maxPower([4,4,4,4], 0, 3) == 4

**Tests that the code failed:**

assert maxPower([1,2,4,5,0], 1, 2) == 5 # ERROR: This unit test fails. Output was 1, but expected value was: 5","(False, True)",0
115,maximize-the-minimum-powered-city,"from typing import List

def maxPower(stations: List[int], r: int, k: int) -> int:
    def is_possible(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        window_sum = sum(stations[:r+1])
        
        for i in range(len(stations)):
            if i > r:
                window_sum -= stations[i - r - 1] + additional_stations[i - r - 1]
            
            if window_sum < min_power:
                needed = min_power - window_sum
                if total_additional + needed > k:
                    return False
                if i + r < len(stations):
                    additional_stations[i + r] += needed
                window_sum += needed
                total_additional += needed
            
            if i + r + 1 < len(stations):
                window_sum += stations[i + r + 1] + additional_stations[i + r + 1]
        
        return True

    low, high = 0, max(stations) + k
    while low < high:
        mid = (low + high + 1) // 2
        if is_possible(mid):
            low = mid
        else:
            high = mid - 1
    
    return low",91,False,12,30,,"**Tests that the code passed:**

assert maxPower([1,2,4,5,0], 1, 2) == 5
assert maxPower([4,4,4,4], 0, 3) == 4

**Tests that the code failed:**

No tests failed.
","(True, True)",1
116,count-anagrams,"
def countAnagrams(s: str) -> int:
    from collections import Counter
    from math import factorial
    MOD = 10**9 + 7

    def count_word_anagrams(word):
        freq = Counter(word)
        word_len = len(word)
        denom = 1
        for count in freq.values():
            denom *= factorial(count)
        return factorial(word_len) // denom

    words = s.split()
    total_anagrams = 1
    for word in words:
        total_anagrams *= count_word_anagrams(word)
        total_anagrams %= MOD

    return total_anagrams
",91,True,41,41,1666 ms,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
117,count-anagrams,"def countAnagrams(s: str) -> int:
    from collections import Counter
    from math import factorial
    MOD = 10**9 + 7

    def count_word_anagrams(word):
        """"""
        Calculate the number of distinct anagrams for a given word.
        
        Parameters:
        word (str): The word to calculate anagrams for.
        
        Returns:
        int: The number of distinct anagrams of the word.
        """"""
        freq = Counter(word)
        word_len = len(word)
        denominator = 1
        for count in freq.values():
            denominator *= factorial(count) % MOD
            denominator %= MOD
        return (factorial(word_len) % MOD) * pow(denominator, MOD-2, MOD) % MOD

    words = s.split()
    total_anagrams = 1
    for word in words:
        total_anagrams *= count_word_anagrams(word)
        total_anagrams %= MOD

    return total_anagrams",91,True,41,41,1448 ms,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
118,number-of-great-partitions,"
from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    dp = [0] * (total_sum + 1)
    dp[0] = 1
    
    for num in nums:
        for j in range(total_sum, num - 1, -1):
            dp[j] = (dp[j] + dp[j - num]) % MOD
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp[i]) % MOD
    
    return result
",91,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
119,number-of-great-partitions,"from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    # Sort nums to potentially simplify the partitioning process
    nums.sort()
    
    # Use a more space-efficient data structure
    dp = {0: 1}
    
    for num in nums:
        new_dp = dp.copy()
        for s in dp:
            if s + num <= total_sum:
                new_dp[s + num] = (new_dp.get(s + num, 0) + dp[s]) % MOD
        dp = new_dp
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp.get(i, 0)) % MOD
    
    return result",91,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
120,cycle-length-queries-in-a-tree,"
from typing import List

def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node):
        path = []
        while node > 0:
            path.append(node)
            node //= 2
        return path

    def find_cycle_length(a, b):
        path_a = find_path_to_root(a)
        path_b = find_path_to_root(b)
        i = len(path_a) - 1
        j = len(path_b) - 1
        while i >= 0 and j >= 0 and path_a[i] == path_b[j]:
            i -= 1
            j -= 1
        return (i + 1) + (j + 1) + 1

    answer = []
    for a, b in queries:
        answer.append(find_cycle_length(a, b))
    return answer
",91,True,27,27,947 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
121,cycle-length-queries-in-a-tree,"from typing import List, Dict

def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node: int, path_cache: Dict[int, List[int]]) -> List[int]:
        if node in path_cache:
            return path_cache[node]
        path = []
        current = node
        while current > 0:
            path.append(current)
            current //= 2
        path_cache[node] = path
        return path

    def find_cycle_length(a: int, b: int, path_cache: Dict[int, List[int]]) -> int:
        path_a = find_path_to_root(a, path_cache)
        path_b = find_path_to_root(b, path_cache)
        i = len(path_a) - 1
        j = len(path_b) - 1
        while i >= 0 and j >= 0 and path_a[i] == path_b[j]:
            i -= 1
            j -= 1
        return (i + 1) + (j + 1) + 1

    path_cache = {}
    answer = []
    for a, b in queries:
        answer.append(find_cycle_length(a, b, path_cache))
    return answer",91,True,27,27,1190 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
122,add-edges-to-make-degrees-of-all-nodes-even,"
from typing import List
from collections import defaultdict

def isPossible(n: int, edges: List[List[int]]) -> bool:
    degree = [0] * (n + 1)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    odd_degree_nodes = [i for i in range(1, n + 1) if degree[i] % 2 == 1]

    if len(odd_degree_nodes) == 0:
        return True
    if len(odd_degree_nodes) == 2:
        return True
    if len(odd_degree_nodes) == 4:
        u, v, w, x = odd_degree_nodes
        if not any((u, v) in edges or (v, u) in edges for u, v in [(u, v), (u, w), (u, x), (v, w), (v, x), (w, x)]):
            return True
    return False
",91,False,0,52,,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True

**Tests that the code failed:**

assert isPossible(4, [[1,2],[1,3],[1,4]]) == False # ERROR: This unit test fails. Output was True, but expected value was: False","(True, True, False)",0
123,add-edges-to-make-degrees-of-all-nodes-even,"from typing import List
from collections import defaultdict

def isPossible(n: int, edges: List[List[int]]) -> bool:
    def find_connected_components(n, edges):
        parent = list(range(n + 1))
        
        def find(x):
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]
        
        def union(x, y):
            rootX = find(x)
            rootY = find(y)
            if rootX != rootY:
                parent[rootY] = rootX
        
        for u, v in edges:
            union(u, v)
        
        components = defaultdict(list)
        for i in range(1, n + 1):
            components[find(i)].append(i)
        
        return list(components.values())
    
    degree = defaultdict(int)
    edge_set = set((min(u, v), max(u, v)) for u, v in edges)
    
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    odd_degree_nodes = [node for node, deg in degree.items() if deg % 2 == 1]

    if len(odd_degree_nodes) == 0:
        return True
    if len(odd_degree_nodes) == 2:
        u, v = odd_degree_nodes
        if (min(u, v), max(u, v)) not in edge_set:
            return True
        for i in range(1, n + 1):
            if i != u and i != v and (min(u, i), max(u, i)) not in edge_set and (min(v, i), max(v, i)) not in edge_set:
                return True
    if len(odd_degree_nodes) == 4:
        u, v, w, x = odd_degree_nodes
        possible_edges = [(u, v), (u, w), (u, x), (v, w), (v, x), (w, x)]
        if not any((min(a, b), max(a, b)) in edge_set for a, b in possible_edges):
            return True
        for i in range(1, n + 1):
            if i not in odd_degree_nodes:
                if (min(u, i), max(u, i)) not in edge_set and (min(v, i), max(v, i)) not in edge_set:
                    if (min(w, i), max(w, i)) not in edge_set and (min(x, i), max(x, i)) not in edge_set:
                        return True
    
    components = find_connected_components(n, edges)
    if len(components) > 1:
        odd_components = [comp for comp in components if any(degree[node] % 2 == 1 for node in comp)]
        if len(odd_components) == 2:
            return True
    
    return False",91,False,46,52,,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True
assert isPossible(4, [[1,2],[1,3],[1,4]]) == False

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
124,minimum-total-cost-to-make-arrays-unequal,"
from typing import List

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    swaps = []
    
    for i in range(n):
        if nums1[i] == nums2[i]:
            swaps.append(i)
    
    if not swaps:
        return 0
    
    swaps.sort()
    total_cost = 0
    
    for i in range(len(swaps)):
        total_cost += swaps[i]
        if i > 0 and swaps[i] == swaps[i-1]:
            return -1
    
    return total_cost
",91,False,79,104,,"**Tests that the code passed:**

assert minimumTotalCost([1,2,3,4,5], [1,2,3,4,5]) == 10

**Tests that the code failed:**

assert minimumTotalCost([2,2,2,1,3], [1,2,2,3,3]) == 10 # ERROR: This unit test fails. Output was 7, but expected value was: 10
assert minimumTotalCost([1,2,2], [1,2,2]) == -1 # ERROR: This unit test fails. Output was 3, but expected value was: -1","(True, False, False)",0
125,minimum-total-cost-to-make-arrays-unequal,"from typing import List
import heapq

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    if n == 0:
        return 0
    
    # Check if already satisfying the condition
    if all(nums1[i] != nums2[i] for i in range(n)):
        return 0
    
    # Priority queue to store potential swaps with their costs
    pq = []
    for i in range(n):
        for j in range(i + 1, n):
            if nums1[i] == nums2[i] or nums1[j] == nums2[j]:
                heapq.heappush(pq, (i + j, i, j))
    
    total_cost = 0
    while pq:
        cost, i, j = heapq.heappop(pq)
        if nums1[i] == nums2[i] or nums1[j] == nums2[j]:
            nums1[i], nums1[j] = nums1[j], nums1[i]
            total_cost += cost
            if all(nums1[k] != nums2[k] for k in range(n)):
                return total_cost
    
    return -1",91,False,15,104,,,,1
126,maximum-number-of-points-from-grid-queries,"
from typing import List
from collections import deque

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    def bfs(query):
        m, n = len(grid), len(grid[0])
        visited = [[False] * n for _ in range(m)]
        queue = deque([(0, 0)])
        points = 0
        
        while queue:
            x, y = queue.popleft()
            if visited[x][y]:
                continue
            visited[x][y] = True
            
            if grid[x][y] < query:
                points += 1
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < m and 0 <= ny < n and not visited[nx][ny]:
                        queue.append((nx, ny))
        
        return points
    
    return [bfs(query) for query in queries]
",91,False,17,21,,"**Tests that the code passed:**

assert maxPoints([[1,2,3],[2,5,7],[3,5,1]], [5,6,2]) == [5,8,1]
assert maxPoints([[5,2,1],[1,1,2]], [3]) == [0]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
127,maximum-number-of-points-from-grid-queries,"from typing import List
from collections import deque
import heapq

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    def bfs(query):
        m, n = len(grid), len(grid[0])
        visited = set()
        queue = [(grid[0][0], 0, 0)]
        points = 0
        
        while queue:
            value, x, y = heapq.heappop(queue)
            if (x, y) in visited:
                continue
            visited.add((x, y))
            
            if value < query:
                points += 1
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < m and 0 <= ny < n and (nx, ny) not in visited:
                        heapq.heappush(queue, (grid[nx][ny], nx, ny))
        
        return points
    
    return [bfs(query) for query in queries]",91,False,16,21,,"**Tests that the code passed:**

assert maxPoints([[1,2,3],[2,5,7],[3,5,1]], [5,6,2]) == [5,8,1]
assert maxPoints([[5,2,1],[1,1,2]], [3]) == [0]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
128,divide-nodes-into-the-maximum-number-of-groups,"
from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1
        
        return max_depth + 1

    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    visited_global = set()
    max_groups = 0
    
    for node in range(1, n + 1):
        if node not in visited_global:
            result = bfs(node)
            if result == -1:
                return -1
            max_groups += result
            visited_global.update(graph[node])
    
    return max_groups
",91,False,11,55,,"**Tests that the code passed:**

assert magnificentSets(3, [[1,2],[2,3],[3,1]]) == -1

**Tests that the code failed:**

assert magnificentSets(6, [[1,2],[1,4],[1,5],[2,6],[2,3],[4,6]]) == 4 # ERROR: This unit test fails. Output was 11, but expected value was: 4","(False, True)",0
129,divide-nodes-into-the-maximum-number-of-groups,"from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1, visited
        
        return max_depth + 1, visited

    def merge_results(components):
        max_groups = 0
        for depth in components:
            if depth == -1:
                return -1
            max_groups = max(max_groups, depth)
        return max_groups

    def check_compatibility(components):
        for i in range(len(components)):
            for j in range(i + 1, len(components)):
                if not is_compatible(components[i], components[j]):
                    return False
        return True

    def is_compatible(comp1, comp2):
        for node1 in comp1:
            for node2 in comp2:
                if abs(comp1[node1] - comp2[node2]) != 1:
                    return False
        return True

    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    visited_global = set()
    components = []
    
    for node in range(1, n + 1):
        if node not in visited_global:
            result, visited = bfs(node)
            if result == -1:
                return -1
            components.append(visited)
            visited_global.update(visited.keys())
    
    if not check_compatibility(components):
        return -1
    
    return merge_results([len(comp) for comp in components])",91,False,10,55,,,,1
130,count-palindromic-subsequences,"
def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Initialize for subsequences of length 1
    for i in range(n):
        dp[i][i][1] = 1
    
    # Fill the dp table
    for length in range(2, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            for k in range(1, 6):
                dp[i][j][k] = dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]
                if s[i] == s[j]:
                    if k == 2:
                        dp[i][j][k] += 1
                    elif k > 2:
                        dp[i][j][k] += dp[i + 1][j - 1][k - 2]
                dp[i][j][k] = (dp[i][j][k] + MOD) % MOD
    
    # The result is the number of palindromic subsequences of length 5 in s[0:n-1]
    return dp[0][n - 1][5]
",91,False,0,63,,"**Tests that the code passed:**

assert countPalindromes(""103301"") == 2
assert countPalindromes(""0000000"") == 21
assert countPalindromes(""9999900000"") == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
131,count-palindromic-subsequences,"from collections import defaultdict

def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    if n < 5:
        return 0
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Initialize for subsequences of length 1
    for i in range(n):
        dp[i][i][1] = 1
    
    # Fill the dp table
    for length in range(2, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            if s[i] == s[j]:
                for k in range(2, 6):
                    dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k] + (1 if k == 2 else 0)) % MOD
            else:
                for k in range(2, 6):
                    dp[i][j][k] = (dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
    
    # Count palindromic subsequences of length 5
    count = 0
    for i in range(n):
        for j in range(i + 4, n):
            if s[i] == s[j]:
                count = (count + dp[i + 1][j - 1][3]) % MOD
    
    return count",91,False,13,63,,,,1
132,count-subarrays-with-median-k,"
from typing import List

def countSubarrays(nums: List[int], k: int) -> int:
    def count_medians(arr, k):
        count = 0
        for i in range(len(arr)):
            for j in range(i, len(arr)):
                subarray = arr[i:j+1]
                subarray.sort()
                mid = (len(subarray) - 1) // 2
                if subarray[mid] == k:
                    count += 1
        return count

    return count_medians(nums, k)
",91,False,39,45,,"**Tests that the code passed:**

assert countSubarrays([3,2,1,4,5], 4) == 3
assert countSubarrays([2,3,1], 3) == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
133,count-subarrays-with-median-k,"from typing import List
from collections import defaultdict

def countSubarrays(nums: List[int], k: int) -> int:
    def count_medians(arr, k):
        n = len(arr)
        count = 0
        balance = 0
        balance_count = defaultdict(int)
        balance_count[0] = 1
        k_index = arr.index(k)
        found_k = False

        for i in range(n):
            if arr[i] < k:
                balance -= 1
            elif arr[i] > k:
                balance += 1
            else:
                found_k = True

            if found_k:
                count += balance_count[balance - 1]
            else:
                balance_count[balance] += 1

        return count

    return count_medians(nums, k)",91,False,0,45,,,,1
134,number-of-beautiful-partitions,"
def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    
    def is_beautiful(start, end):
        return s[start] in prime_digits and s[end] not in prime_digits
    
    n = len(s)
    dp = [[0] * (k + 1) for _ in range(n + 1)]
    dp[0][0] = 1
    
    for i in range(1, n + 1):
        for j in range(1, k + 1):
            for l in range(minLength, i + 1):
                if is_beautiful(i - l, i - 1):
                    dp[i][j] = (dp[i][j] + dp[i - l][j - 1]) % MOD
    
    return dp[n][k]
",91,False,36,74,,"**Tests that the code passed:**

assert beautifulPartitions(""23542185131"", 3, 2) == 3
assert beautifulPartitions(""23542185131"", 3, 3) == 1
assert beautifulPartitions(""3312958"", 3, 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
135,number-of-beautiful-partitions,"def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    
    def is_beautiful(start, end):
        return s[start] in prime_digits and s[end] not in prime_digits
    
    n = len(s)
    dp = [[0] * (k + 1) for _ in range(n + 1)]
    dp[0][0] = 1
    
    # Precompute valid partitions
    valid_partitions = [[False] * n for _ in range(n)]
    for i in range(n):
        for j in range(i + minLength - 1, n):
            if is_beautiful(i, j):
                valid_partitions[i][j] = True
    
    # Optimize DP transitions
    for j in range(1, k + 1):
        prefix_sum = [0] * (n + 1)
        for i in range(1, n + 1):
            prefix_sum[i] = (prefix_sum[i - 1] + dp[i - 1][j - 1]) % MOD
            for l in range(minLength, i + 1):
                if valid_partitions[i - l][i - 1]:
                    dp[i][j] = (dp[i][j] + dp[i - l][j - 1]) % MOD
    
    return dp[n][k]",91,False,39,74,,"**Tests that the code passed:**

assert beautifulPartitions(""23542185131"", 3, 2) == 3
assert beautifulPartitions(""23542185131"", 3, 3) == 1
assert beautifulPartitions(""3312958"", 3, 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
136,split-message-based-on-limit,"
def splitMessage(message: str, limit: int) -> List[str]:
    from typing import List
    
    def calculate_suffix_length(parts: int) -> int:
        return len(f""<{parts}/{parts}>"")
    
    def can_split_into_parts(parts: int) -> bool:
        suffix_length = calculate_suffix_length(parts)
        total_length = sum(limit - suffix_length for _ in range(parts))
        return total_length >= len(message)
    
    # Find the minimum number of parts needed
    parts = 1
    while not can_split_into_parts(parts):
        parts += 1
    
    result = []
    suffix_length = calculate_suffix_length(parts)
    idx = 0
    
    for part in range(1, parts + 1):
        current_suffix = f""<{part}/{parts}>""
        current_limit = limit - len(current_suffix)
        current_part = message[idx:idx + current_limit] + current_suffix
        result.append(current_part)
        idx += current_limit
    
    return result
",91,False,2,94,,"**Tests that the code passed:**

assert splitMessage(""short message"", 15) == [""short mess<1/2>"",""age<2/2>""]

**Tests that the code failed:**

assert splitMessage(""this is really a very awesome message"", 9) == [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""] # ERROR: This unit test fails. Output was ['thi<1/19>', 's i<2/19>', 's r<3/19>', 'eal<4/19>', 'ly <5/19>', 'a v<6/19>', 'ery<7/19>', ' aw<8/19>', 'eso<9/19>', 'me<10/19>', ' m<11/19>', 'es<12/19>', 'sa<13/19>', 'ge<14/19>', '<15/19>', '<16/19>', '<17/19>', '<18/19>', '<19/19>'], but expected value was: [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""]","(False, True)",0
137,split-message-based-on-limit,"from typing import List

def splitMessage(message: str, limit: int) -> List[str]:
    def calculate_suffix_length(parts: int) -> int:
        return len(f""<{parts}/{parts}>"")
    
    def can_split_into_parts(parts: int) -> bool:
        total_length = 0
        for part in range(1, parts + 1):
            suffix_length = len(f""<{part}/{parts}>"")
            total_length += (limit - suffix_length)
        return total_length >= len(message)
    
    # Binary search to find the minimum number of parts needed
    low, high = 1, len(message)
    while low < high:
        mid = (low + high) // 2
        if can_split_into_parts(mid):
            high = mid
        else:
            low = mid + 1
    
    parts = low
    result = []
    idx = 0
    
    for part in range(1, parts + 1):
        current_suffix = f""<{part}/{parts}>""
        current_limit = limit - len(current_suffix)
        if current_limit <= 0:
            return []
        current_part = message[idx:idx + current_limit] + current_suffix
        result.append(current_part)
        idx += current_limit
    
    return result",91,False,87,94,,"**Tests that the code passed:**

assert splitMessage(""this is really a very awesome message"", 9) == [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""]
assert splitMessage(""short message"", 15) == [""short mess<1/2>"",""age<2/2>""]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
138,maximum-number-of-non-overlapping-palindrome-substrings,"
def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    dp = [0] * (n + 1)

    for i in range(n - k + 1):
        for j in range(i + k, n + 1):
            if is_palindrome(s[i:j]):
                dp[j] = max(dp[j], dp[i] + 1)

    return max(dp)
",91,False,12,56,,"**Tests that the code passed:**

assert maxPalindromes(""adbcda"", 2) == 0

**Tests that the code failed:**

assert maxPalindromes(""abaccdbbd"", 3) == 2 # ERROR: This unit test fails. Output was 1, but expected value was: 2","(False, True)",0
139,maximum-number-of-non-overlapping-palindrome-substrings,"from typing import List

def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    dp = [0] * (n + 1)
    palindromes = [[False] * n for _ in range(n)]

    # Preprocess to find all palindromic substrings
    for i in range(n):
        palindromes[i][i] = True
    for i in range(n - 1):
        palindromes[i][i + 1] = (s[i] == s[i + 1])
    for length in range(3, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            palindromes[i][j] = (s[i] == s[j] and palindromes[i + 1][j - 1])

    for i in range(n):
        dp[i + 1] = dp[i]
        for j in range(i + 1):
            if i - j + 1 >= k and palindromes[j][i]:
                dp[i + 1] = max(dp[i + 1], dp[j] + 1)

    return dp[n]",91,True,56,56,3490 ms,"**Tests that the code passed:**

assert maxPalindromes(""abaccdbbd"", 3) == 2
assert maxPalindromes(""adbcda"", 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
140,minimum-total-distance-traveled,"
from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort()
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for i in range(1, n + 1):
        for j in range(1, m + 1):
            dp[i][j] = dp[i][j - 1]
            total_distance = 0
            for k in range(1, min(factory[j - 1][1], i) + 1):
                total_distance += abs(robot[i - k] - factory[j - 1][0])
                dp[i][j] = min(dp[i][j], dp[i - k][j - 1] + total_distance)
    
    return dp[n][m]
",91,False,0,40,,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
141,minimum-total-distance-traveled,"from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort()
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for i in range(1, n + 1):
        for j in range(1, m + 1):
            dp[i][j] = dp[i][j - 1]
            total_distance = 0
            for k in range(1, min(factory[j - 1][1], i) + 1):
                total_distance += abs(robot[i - k] - factory[j - 1][0])
                dp[i][j] = min(dp[i][j], dp[i - k][j - 1] + total_distance)
    
    return dp[n][m]",91,False,0,40,,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",1
142,next-greater-element-iv,"
from typing import List

def secondGreaterElement(nums: List[int]) -> List[int]:
    result = [-1] * len(nums)
    first_greater = []
    second_greater = []

    for i in range(len(nums)):
        while second_greater and nums[second_greater[-1]] < nums[i]:
            idx = second_greater.pop()
            result[idx] = nums[i]
        
        temp = []
        while first_greater and nums[first_greater[-1]] < nums[i]:
            idx = first_greater.pop()
            temp.append(idx)
        
        second_greater.extend(temp)
        first_greater.append(i)
    
    return result
",91,False,19,51,,"**Tests that the code passed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1]
assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
143,next-greater-element-iv,"from typing import List

def secondGreaterElement(nums: List[int]) -> List[int]:
    result = [-1] * len(nums)
    first_greater_stack = []
    second_greater_stack = []

    for i in range(len(nums)):
        # Process elements in the second_greater_stack
        while second_greater_stack and nums[second_greater_stack[-1]] < nums[i]:
            idx = second_greater_stack.pop()
            result[idx] = nums[i]
        
        temp = []
        # Process elements in the first_greater_stack
        while first_greater_stack and nums[first_greater_stack[-1]] < nums[i]:
            idx = first_greater_stack.pop()
            temp.append(idx)
        
        # Move elements from temp to second_greater_stack
        second_greater_stack.extend(temp)
        # Add current index to first_greater_stack
        first_greater_stack.append(i)
    
    return result",91,False,19,51,,"**Tests that the code passed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1]
assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
144,minimum-number-of-operations-to-make-arrays-similar,"
from typing import List

def makeSimilar(nums: List[int], target: List[int]) -> int:
    nums.sort()
    target.sort()
    
    odd_nums = [x for x in nums if x % 2 != 0]
    even_nums = [x for x in nums if x % 2 == 0]
    odd_target = [x for x in target if x % 2 != 0]
    even_target = [x for x in target if x % 2 == 0]
    
    operations = 0
    
    for i in range(len(odd_nums)):
        operations += abs(odd_nums[i] - odd_target[i]) // 2
    
    for i in range(len(even_nums)):
        operations += abs(even_nums[i] - even_target[i]) // 2
    
    return operations // 2
",91,True,32,32,717 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
145,minimum-number-of-operations-to-make-arrays-similar,"from typing import List

def makeSimilar(nums: List[int], target: List[int]) -> int:
    # Ensure the input arrays are valid and have the same length
    if len(nums) != len(target):
        raise ValueError(""Input arrays must have the same length"")
    
    # Sort the arrays to facilitate comparison
    nums.sort()
    target.sort()
    
    # Separate the numbers into odd and even lists
    odd_nums = [x for x in nums if x % 2 != 0]
    even_nums = [x for x in nums if x % 2 == 0]
    odd_target = [x for x in target if x % 2 != 0]
    even_target = [x for x in target if x % 2 == 0]
    
    # Check if the number of odd and even elements are the same
    if len(odd_nums) != len(odd_target) or len(even_nums) != len(even_target):
        raise ValueError(""The number of odd and even elements in nums and target must be the same"")
    
    operations = 0
    
    # Calculate the number of operations needed for odd numbers
    for i in range(len(odd_nums)):
        operations += abs(odd_nums[i] - odd_target[i]) // 2
    
    # Calculate the number of operations needed for even numbers
    for i in range(len(even_nums)):
        operations += abs(even_nums[i] - even_target[i]) // 2
    
    # Each operation affects two elements, so divide the total by 2
    return operations // 2",91,True,32,32,728 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
146,minimum-cost-to-make-array-equal,"
from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    left, right = min(nums), max(nums)
    while left < right:
        mid = (left + right) // 2
        cost1 = get_cost(mid)
        cost2 = get_cost(mid + 1)
        
        if cost1 < cost2:
            right = mid
        else:
            left = mid + 1
    
    return get_cost(left)
",91,True,48,48,595 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
147,minimum-cost-to-make-array-equal,"from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    def weighted_median(nums, cost):
        pairs = sorted(zip(nums, cost))
        total_cost = sum(cost)
        cumulative_cost = 0
        for num, c in pairs:
            cumulative_cost += c
            if cumulative_cost >= total_cost / 2:
                return num
    
    left, right = min(nums), max(nums)
    median = weighted_median(nums, cost)
    min_cost = get_cost(median)
    
    while left < right:
        mid = (left + right) // 2
        mid_cost = get_cost(mid)
        mid_plus_one_cost = get_cost(mid + 1)
        
        if mid_cost < mid_plus_one_cost:
            right = mid
        else:
            left = mid + 1
    
    return min(min_cost, get_cost(left))",91,True,48,48,649 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
148,create-components-with-same-value,"
from typing import List
from collections import defaultdict, deque

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = [False] * n
        def dfs(node):
            visited[node] = True
            total = nums[node]
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total > target:
                return -1
            return total if total < target else 0
        
        for i in range(n):
            if not visited[i]:
                if dfs(i) != 0:
                    return False
        return True

    n = len(nums)
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    for k in range(n, 0, -1):
        if total_sum % k == 0:
            if can_split(total_sum // k):
                return k - 1
    return 0
",91,True,43,43,1162 ms,"**Tests that the code passed:**

assert componentValue([6,2,2,2,6], [[0,1],[1,2],[1,3],[3,4]]) == 2
assert componentValue([2], []) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
149,create-components-with-same-value,"from typing import List
from collections import defaultdict, deque

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = [False] * n
        def dfs(node):
            visited[node] = True
            total = nums[node]
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total > target:
                return -1
            return total if total < target else 0
        
        for i in range(n):
            if not visited[i]:
                if dfs(i) != 0:
                    return False
        return True

    n = len(nums)
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    for k in range(n, 0, -1):
        if total_sum % k == 0:
            if can_split(total_sum // k):
                return k - 1
    return 0",91,True,43,43,1154 ms,"**Tests that the code passed:**

assert componentValue([6,2,2,2,6], [[0,1],[1,2],[1,3],[3,4]]) == 2
assert componentValue([2], []) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
150,count-subarrays-with-fixed-bounds,"
from typing import List

def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = left_bound = -1
    
    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            left_bound = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        count += max(0, min(min_pos, max_pos) - left_bound)
    
    return count
",91,True,52,52,710 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",0
151,count-subarrays-with-fixed-bounds,"from typing import List

def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = left_bound = -1
    
    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            left_bound = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        if min_pos != -1 and max_pos != -1:
            count += max(0, min(min_pos, max_pos) - left_bound)
    
    return count",91,True,52,52,681 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",1
152,longest-increasing-subsequence-ii,"
def lengthOfLIS(nums: List[int], k: int) -> int:
    from sortedcontainers import SortedList

    dp = SortedList()
    
    for num in nums:
        pos = dp.bisect_left(num - k)
        if pos == len(dp):
            dp.add(num)
        else:
            if dp[pos] < num:
                dp[pos] = num
            else:
                dp.add(num)
    
    return len(dp)
",91,False,0,84,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5 # ERROR: This unit test fails because the function generated: No module named 'sortedcontainers'.
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4 # ERROR: This unit test fails because the function generated: No module named 'sortedcontainers'.
assert lengthOfLIS([1,5], 1) == 1 # ERROR: This unit test fails because the function generated: No module named 'sortedcontainers'.","(False, False, False)",0
153,longest-increasing-subsequence-ii,"def lengthOfLIS(nums: List[int], k: int) -> int:
    from bisect import bisect_left

    dp = []
    
    for num in nums:
        pos = bisect_left(dp, num)
        if pos == len(dp):
            if not dp or num - dp[-1] <= k:
                dp.append(num)
        else:
            if num - dp[pos - 1] <= k:
                dp[pos] = num
    
    return len(dp)",91,False,76,84,,"**Tests that the code passed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4
assert lengthOfLIS([1,5], 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
154,paths-in-matrix-whose-sum-is-divisible-by-k,"
from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # dp[i][j][r] will store the number of paths to (i, j) with sum % k == r
    dp = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp[i][j][r] > 0:
                    if i + 1 < m:
                        dp[i + 1][j][(r + grid[i + 1][j]) % k] = (dp[i + 1][j][(r + grid[i + 1][j]) % k] + dp[i][j][r]) % MOD
                    if j + 1 < n:
                        dp[i][j + 1][(r + grid[i][j + 1]) % k] = (dp[i][j + 1][(r + grid[i][j + 1]) % k] + dp[i][j][r]) % MOD
    
    return dp[m - 1][n - 1][0]
",91,True,88,88,2544 ms,"**Tests that the code passed:**

assert numberOfPaths([[5,2,4],[3,0,5],[0,7,2]], 3) == 2
assert numberOfPaths([[0,0]], 5) == 1
assert numberOfPaths([[7,3,4,9],[2,3,6,2],[2,3,7,0]], 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
155,paths-in-matrix-whose-sum-is-divisible-by-k,"from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # Use two 3D arrays to optimize space complexity
    dp_prev = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp_prev[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp_prev[i][j][r] > 0:
                    if i + 1 < m:
                        dp_prev[i + 1][j][(r + grid[i + 1][j]) % k] = (dp_prev[i + 1][j][(r + grid[i + 1][j]) % k] + dp_prev[i][j][r]) % MOD
                    if j + 1 < n:
                        dp_prev[i][j + 1][(r + grid[i][j + 1]) % k] = (dp_prev[i][j + 1][(r + grid[i][j + 1]) % k] + dp_prev[i][j][r]) % MOD
    
    return dp_prev[m - 1][n - 1][0]",91,True,88,88,2586 ms,"**Tests that the code passed:**

assert numberOfPaths([[5,2,4],[3,0,5],[0,7,2]], 3) == 2
assert numberOfPaths([[0,0]], 5) == 1
assert numberOfPaths([[7,3,4,9],[2,3,6,2],[2,3,7,0]], 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
156,minimum-time-to-visit-a-cell-in-a-grid,"
from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    visited = set()

    while pq:
        time, row, col = heapq.heappop(pq)
        if (row, col) in visited:
            continue
        visited.add((row, col))
        
        if row == m - 1 and col == n - 1:
            return time
        
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n and (new_row, new_col) not in visited:
                new_time = max(time + 1, grid[new_row][new_col])
                heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1
",17,False,22,41,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minimumTime([[0,1,3,2],[5,1,2,5],[4,3,8,6]]) == 7 # ERROR: This unit test fails. Output was 6, but expected value was: 7
assert minimumTime([[0,2,4],[3,2,1],[1,0,4]]) == -1 # ERROR: This unit test fails. Output was 5, but expected value was: -1","(False, False)",0
157,minimum-time-to-visit-a-cell-in-a-grid,"from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    min_time = [[float('inf')] * n for _ in range(m)]
    min_time[0][0] = 0

    while pq:
        time, row, col = heapq.heappop(pq)
        
        if row == m - 1 and col == n - 1:
            return time
        
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n:
                new_time = max(time + 1, grid[new_row][new_col])
                if new_time < min_time[new_row][new_col]:
                    min_time[new_row][new_col] = new_time
                    heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1",17,False,22,41,,,,1
158,find-the-string-with-lcp,"
from typing import List

def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    word = [''] * n
    current_char = 'a'
    
    for i in range(n):
        if word[i] == '':
            word[i] = current_char
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    word[j] = word[i]
            current_char = chr(ord(current_char) + 1)
    
    for i in range(n):
        for j in range(n):
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    return ''.join(word)
",17,False,36,55,,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
159,find-the-string-with-lcp,"from typing import List
import heapq

def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    word = [''] * n
    current_char = 'a'
    
    # Priority queue to ensure lexicographical order
    pq = []
    
    for i in range(n):
        if word[i] == '':
            if current_char > 'z':
                return """"
            word[i] = current_char
            heapq.heappush(pq, (current_char, i))
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    word[j] = word[i]
            current_char = chr(ord(current_char) + 1)
    
    while pq:
        char, i = heapq.heappop(pq)
        for j in range(n):
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    for i in range(n):
        if lcp[i][i] != n - i:
            return """"
        for j in range(n):
            if lcp[i][j] != lcp[j][i]:
                return """"
    
    return ''.join(word)",17,True,55,55,1456 ms,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
160,handling-sum-queries-after-update,"
from typing import List

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    result = []
    
    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            for i in range(l, r + 1):
                nums1[i] = 1 - nums1[i]
        elif query[0] == 2:
            p = query[1]
            for i in range(len(nums2)):
                nums2[i] += nums1[i] * p
        elif query[0] == 3:
            result.append(sum(nums2))
    
    return result
",17,False,72,77,,"**Tests that the code passed:**

assert handleQuery([1,0,1], [0,0,0], [[1,1,1],[2,1,0],[3,0,0]]) == [3]
assert handleQuery([1], [5], [[2,0,0],[3,0,0]]) == [5]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
161,handling-sum-queries-after-update,"from typing import List

class SegmentTree:
    def __init__(self, n):
        self.n = n
        self.tree = [0] * (4 * n)
        self.lazy = [0] * (4 * n)

    def update_range(self, start, end, l, r, node, value):
        if self.lazy[node] != 0:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > end or start > r or end < l:
            return

        if start >= l and end <= r:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            return

        mid = (start + end) // 2
        self.update_range(start, mid, l, r, node * 2 + 1, value)
        self.update_range(mid + 1, end, l, r, node * 2 + 2, value)
        self.tree[node] = self.tree[node * 2 + 1] + self.tree[node * 2 + 2]

    def query_range(self, start, end, l, r, node):
        if self.lazy[node] != 0:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > end or start > r or end < l:
            return 0

        if start >= l and end <= r:
            return self.tree[node]

        mid = (start + end) // 2
        left_query = self.query_range(start, mid, l, r, node * 2 + 1)
        right_query = self.query_range(mid + 1, end, l, r, node * 2 + 2)
        return left_query + right_query

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    n = len(nums1)
    result = []
    seg_tree = SegmentTree(n)
    
    for i in range(n):
        if nums1[i] == 1:
            seg_tree.update_range(0, n - 1, i, i, 0, 1)
    
    total_addition = 0
    
    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            seg_tree.update_range(0, n - 1, l, r, 0, 1)
        elif query[0] == 2:
            p = query[1]
            total_addition += p * seg_tree.query_range(0, n - 1, 0, n - 1, 0)
        elif query[0] == 3:
            result.append(sum(nums2) + total_addition)
    
    return result",17,False,0,77,,"**Tests that the code passed:**

assert handleQuery([1,0,1], [0,0,0], [[1,1,1],[2,1,0],[3,0,0]]) == [3]
assert handleQuery([1], [5], [[2,0,0],[3,0,0]]) == [5]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
162,subsequence-with-the-minimum-score,"
def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Calculate the leftmost positions where t can be a subsequence of s
    left = [-1] * m
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            left[j] = i
            j += 1
    
    # Calculate the rightmost positions where t can be a subsequence of s
    right = [-1] * m
    j = m - 1
    for i in range(n - 1, -1, -1):
        if j >= 0 and s[i] == t[j]:
            right[j] = i
            j -= 1
    
    # If t is already a subsequence of s, the minimum score is 0
    if left[-1] != -1:
        return 0
    
    # Calculate the minimum score by removing characters from t
    min_score = float('inf')
    for i in range(m):
        if left[i] != -1:
            min_score = min(min_score, right[i] - left[i] + 1)
    
    return min_score
",17,False,0,54,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minimumScore(""abacaba"", ""bzaa"") == 1 # ERROR: This unit test fails. Output was -1, but expected value was: 1
assert minimumScore(""cde"", ""xyz"") == 3 # ERROR: This unit test fails. Output was inf, but expected value was: 3","(False, False)",0
163,subsequence-with-the-minimum-score,"from typing import List

def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Calculate the leftmost positions where t can be a subsequence of s
    left = [-1] * m
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            left[j] = i
            j += 1
    
    # Calculate the rightmost positions where t can be a subsequence of s
    right = [-1] * m
    j = m - 1
    for i in range(n - 1, -1, -1):
        if j >= 0 and s[i] == t[j]:
            right[j] = i
            j -= 1
    
    # If t is already a subsequence of s, the minimum score is 0
    if left[-1] != -1:
        return 0
    
    # Calculate the minimum score by removing characters from t using a two-pointer technique
    min_score = float('inf')
    j = 0
    for i in range(m):
        while j < m and (right[j] == -1 or right[j] < left[i]):
            j += 1
        if j < m and left[i] != -1 and right[j] != -1:
            min_score = min(min_score, right[j] - left[i] + 1)
    
    # If no valid indices are found, return the length of t
    if min_score == float('inf'):
        return m
    
    return min_score",17,False,-1,-1,-1,,,1
164,minimum-number-of-visited-cells-in-a-grid,"
from typing import List
from collections import deque

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0)]  # rightward and downward movements
    queue = deque([(0, 0, 1)])  # (row, col, steps)
    visited = set((0, 0))
    
    while queue:
        i, j, steps = queue.popleft()
        
        if i == m - 1 and j == n - 1:
            return steps
        
        for di, dj in directions:
            for k in range(1, grid[i][j] + 1):
                ni, nj = i + di * k, j + dj * k
                if 0 <= ni < m and 0 <= nj < n and (ni, nj) not in visited:
                    visited.add((ni, nj))
                    queue.append((ni, nj, steps + 1))
    
    return -1
",17,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
165,minimum-number-of-visited-cells-in-a-grid,"from typing import List
from collections import deque

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0)]  # rightward and downward movements
    queue = deque([(1, 0, 0)])  # (steps, row, col)
    visited = set([(0, 0)])
    
    while queue:
        steps, i, j = queue.popleft()
        
        if i == m - 1 and j == n - 1:
            return steps
        
        for di, dj in directions:
            for k in range(1, grid[i][j] + 1):
                ni, nj = i + di * k, j + dj * k
                if 0 <= ni < m and 0 <= nj < n and (ni, nj) not in visited:
                    visited.add((ni, nj))
                    queue.append((steps + 1, ni, nj))
    
    return -1",17,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
166,rearranging-fruits,"
from typing import List
from collections import Counter

def minCost(basket1: List[int], basket2: List[int]) -> int:
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(basket1 + basket2):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the minimum cost to make both baskets equal
    swaps = []
    for fruit in set(basket1 + basket2):
        diff = abs(count1[fruit] - count2[fruit]) // 2
        if count1[fruit] > count2[fruit]:
            swaps.extend([fruit] * diff)
        elif count2[fruit] > count1[fruit]:
            swaps.extend([fruit] * diff)
    
    swaps.sort()
    min_cost = 0
    for i in range(len(swaps) // 2):
        min_cost += swaps[i]
    
    return min_cost
",17,False,20,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
167,rearranging-fruits,"from typing import List
from collections import Counter
import heapq

def minCost(basket1: List[int], basket2: List[int]) -> int:
    # Check if baskets are already equal
    if sorted(basket1) == sorted(basket2):
        return 0
    
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(basket1 + basket2):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the minimum cost to make both baskets equal
    swaps = []
    for fruit in set(basket1 + basket2):
        diff = abs(count1[fruit] - count2[fruit]) // 2
        if count1[fruit] > count2[fruit]:
            swaps.extend([fruit] * diff)
        elif count2[fruit] > count1[fruit]:
            swaps.extend([fruit] * diff)
    
    # Use a min-heap to find the smallest elements efficiently
    heapq.heapify(swaps)
    min_cost = 0
    for _ in range(len(swaps) // 2):
        min_cost += heapq.heappop(swaps)
    
    return min_cost",17,False,20,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
168,lexicographically-smallest-beautiful-string,"
def smallestBeautifulString(s: str, k: int) -> str:
    def is_beautiful(s: str) -> bool:
        for i in range(len(s) - 1):
            if s[i] == s[i + 1]:
                return False
        for i in range(len(s) - 2):
            if s[i] == s[i + 2]:
                return False
        return True

    n = len(s)
    s = list(s)
    
    for i in range(n - 1, -1, -1):
        for j in range(ord(s[i]) - ord('a') + 1, k):
            s[i] = chr(ord('a') + j)
            if is_beautiful(s[:i + 1]):
                for l in range(i + 1, n):
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s[:l + 1]):
                            break
                return ''.join(s)
        s[i] = 'a'
    
    return """"
",17,False,1042,1054,,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",0
169,lexicographically-smallest-beautiful-string,"def smallestBeautifulString(s: str, k: int) -> str:
    def is_beautiful(s: str, idx: int) -> bool:
        if idx > 0 and s[idx] == s[idx - 1]:
            return False
        if idx > 1 and s[idx] == s[idx - 2]:
            return False
        return True

    n = len(s)
    s = list(s)
    
    for i in range(n - 1, -1, -1):
        for j in range(ord(s[i]) - ord('a') + 1, k):
            s[i] = chr(ord('a') + j)
            if is_beautiful(s, i):
                for l in range(i + 1, n):
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s, l):
                            break
                return ''.join(s)
        s[i] = 'a'
    
    return """"",17,True,1054,1054,267 ms,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",1
170,count-increasing-quadruplets,"
def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    for i in range(n - 3):
        for j in range(i + 1, n - 2):
            for k in range(j + 1, n - 1):
                for l in range(k + 1, n):
                    if nums[i] < nums[k] < nums[j] < nums[l]:
                        count += 1
                        
    return count
",17,False,63,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2
assert countQuadruplets([1,2,3,4]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
171,count-increasing-quadruplets,"from typing import List

def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    # Iterate through all possible quadruplets
    for i in range(n - 3):
        for j in range(i + 1, n - 2):
            for k in range(j + 1, n - 1):
                for l in range(k + 1, n):
                    if nums[i] < nums[k] < nums[j] < nums[l]:
                        count += 1
    
    return count",17,False,63,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2
assert countQuadruplets([1,2,3,4]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
172,put-marbles-in-bags,"
from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    if k == 1:
        return 0
    
    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the minimum and maximum possible scores
    costs.sort()
    
    # The minimum score is the sum of the smallest k-1 costs
    min_score = sum(costs[:k-1])
    
    # The maximum score is the sum of the largest k-1 costs
    max_score = sum(costs[-(k-1):])
    
    return max_score - min_score
",17,True,103,103,541 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
173,put-marbles-in-bags,"from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    if k == 1 or k == n:
        return 0
    
    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the minimum and maximum possible scores
    costs.sort()
    
    # The minimum score is the sum of the smallest k-1 costs
    min_score = sum(costs[:k-1])
    
    # The maximum score is the sum of the largest k-1 costs
    max_score = sum(costs[-(k-1):])
    
    return max_score - min_score",17,True,103,103,549 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
174,shortest-cycle-in-a-graph,"
from typing import List
from collections import deque, defaultdict

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    def bfs(start):
        dist = [-1] * n
        dist[start] = 0
        queue = deque([(start, -1)])
        while queue:
            node, parent = queue.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    queue.append((neighbor, node))
                elif neighbor != parent:
                    return dist[node] + dist[neighbor] + 1
        return float('inf')
    
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i))
    
    return shortest_cycle if shortest_cycle != float('inf') else -1
",17,False,84,88,,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
175,shortest-cycle-in-a-graph,"from typing import List
from collections import deque, defaultdict

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    def build_graph(edges):
        graph = defaultdict(list)
        for u, v in edges:
            graph[u].append(v)
            graph[v].append(u)
        return graph

    def bfs(start, graph):
        dist = [-1] * n
        dist[start] = 0
        queue = deque([(start, -1)])
        while queue:
            node, parent = queue.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    queue.append((neighbor, node))
                elif neighbor != parent:
                    return dist[node] + dist[neighbor] + 1
        return float('inf')

    if not edges:
        return -1

    graph = build_graph(edges)
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i, graph))
        if shortest_cycle == 3:  # Early termination if the shortest possible cycle is found
            break

    return shortest_cycle if shortest_cycle != float('inf') else -1",17,False,84,88,,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
176,minimum-time-to-complete-all-tasks,"
from typing import List

def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = [0] * 2001
    total_time = 0

    for start, end, duration in tasks:
        current_duration = sum(time_on[start:end+1])
        remaining_duration = duration - current_duration

        if remaining_duration > 0:
            for t in range(end, start-1, -1):
                if remaining_duration == 0:
                    break
                if time_on[t] == 0:
                    time_on[t] = 1
                    total_time += 1
                    remaining_duration -= 1

    return total_time
",17,True,1059,1059,301 ms,"**Tests that the code passed:**

assert findMinimumTime([[2,3,1],[4,5,1],[1,5,2]]) == 2
assert findMinimumTime([[1,3,2],[2,5,3],[5,6,2]]) == 4

**Tests that the code failed:**

No tests failed.
","(True, True)",0
177,minimum-time-to-complete-all-tasks,"from typing import List

def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = [0] * 2001
    total_time = 0

    for start, end, duration in tasks:
        current_duration = sum(time_on[start:end+1])
        remaining_duration = duration - current_duration

        if remaining_duration > 0:
            for t in range(end, start - 1, -1):
                if time_on[t] == 0:
                    time_on[t] = 1
                    total_time += 1
                    remaining_duration -= 1
                    if remaining_duration == 0:
                        break

    return total_time",17,True,1059,1059,308 ms,"**Tests that the code passed:**

assert findMinimumTime([[2,3,1],[4,5,1],[1,5,2]]) == 2
assert findMinimumTime([[1,3,2],[2,5,3],[5,6,2]]) == 4

**Tests that the code failed:**

No tests failed.
","(True, True)",1
178,count-number-of-possible-root-nodes,"
from collections import defaultdict, deque
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node)

    def count_correct_guesses(root):
        correct_count = 0
        for u, v in guesses:
            if parent_map[v] == u:
                correct_count += 1
        return correct_count

    n = len(edges) + 1
    tree = build_tree(edges)
    parent_map = [-1] * n

    # Arbitrarily choose node 0 as the initial root and build the parent map
    dfs(0, -1)

    # Count the number of correct guesses for each possible root
    possible_roots = 0
    for root in range(n):
        correct_guesses = count_correct_guesses(root)
        if correct_guesses >= k:
            possible_roots += 1

    return possible_roots
",17,False,35,51,,"**Tests that the code passed:**

assert rootCount([[0,1],[1,2],[2,3],[3,4]], [[1,0],[3,4],[2,1],[3,2]], 1) == 5

**Tests that the code failed:**

assert rootCount([[0,1],[1,2],[1,3],[4,2]], [[1,3],[0,1],[1,0],[2,4]], 3) == 3 # ERROR: This unit test fails. Output was 5, but expected value was: 3","(False, True)",0
179,count-number-of-possible-root-nodes,"from collections import defaultdict, deque
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node)

    def reroot(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                correct_guesses[neighbor] = correct_guesses[node]
                if (node, neighbor) in guess_set:
                    correct_guesses[neighbor] -= 1
                if (neighbor, node) in guess_set:
                    correct_guesses[neighbor] += 1
                reroot(neighbor, node)

    n = len(edges) + 1
    tree = build_tree(edges)
    parent_map = [-1] * n
    guess_set = set((u, v) for u, v in guesses)

    # Arbitrarily choose node 0 as the initial root and build the parent map
    dfs(0, -1)

    # Count the number of correct guesses for the initial root
    correct_guesses = [0] * n
    for u, v in guesses:
        if parent_map[v] == u:
            correct_guesses[0] += 1

    # Reroot the tree and adjust the correct guesses count
    reroot(0, -1)

    # Count the number of possible roots
    possible_roots = sum(1 for count in correct_guesses if count >= k)

    return possible_roots",17,True,51,51,1637 ms,"**Tests that the code passed:**

assert rootCount([[0,1],[1,2],[1,3],[4,2]], [[1,3],[0,1],[1,0],[2,4]], 3) == 3
assert rootCount([[0,1],[1,2],[2,3],[3,4]], [[1,0],[3,4],[2,1],[3,2]], 1) == 5

**Tests that the code failed:**

No tests failed.
","(True, True)",1
180,number-of-ways-to-earn-points,"
from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] means the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # We need to update dp array in reverse to avoid overcounting
        for t in range(target, -1, -1):
            for k in range(1, count + 1):
                if t - k * marks >= 0:
                    dp[t] = (dp[t] + dp[t - k * marks]) % MOD
                else:
                    break
    
    return dp[target]
",17,True,63,63,1172 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
181,number-of-ways-to-earn-points,"from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] means the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # Use a temporary array to store the new dp values
        new_dp = dp[:]
        for t in range(target + 1):
            for k in range(1, count + 1):
                if t + k * marks <= target:
                    new_dp[t + k * marks] = (new_dp[t + k * marks] + dp[t]) % MOD
                else:
                    break
        dp = new_dp
    
    return dp[target]",17,True,63,63,1401 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
182,split-the-array-to-make-coprime-products,"
from typing import List
from math import gcd
from functools import reduce

def findValidSplit(nums: List[int]) -> int:
    def product(arr):
        return reduce(lambda x, y: x * y, arr, 1)
    
    n = len(nums)
    for i in range(n - 1):
        left_product = product(nums[:i + 1])
        right_product = product(nums[i + 1:])
        if gcd(left_product, right_product) == 1:
            return i
    return -1
",17,False,29,75,,"**Tests that the code passed:**

assert findValidSplit([4,7,8,15,3,5]) == 2
assert findValidSplit([4,7,15,8,3,5]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
183,split-the-array-to-make-coprime-products,"from typing import List
from math import gcd

def findValidSplit(nums: List[int]) -> int:
    n = len(nums)
    left_product = 1
    right_product = 1
    
    # Calculate the product of the entire array
    for num in nums:
        right_product *= num
    
    for i in range(n - 1):
        left_product *= nums[i]
        right_product //= nums[i]
        
        if gcd(left_product, right_product) == 1:
            return i
    return -1",17,False,31,75,,"**Tests that the code passed:**

assert findValidSplit([4,7,8,15,3,5]) == 2
assert findValidSplit([4,7,15,8,3,5]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
184,time-to-cross-a-bridge,"
from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = sorted([(i, t[0] + t[2], t) for i, t in enumerate(time)], key=lambda x: (x[1], x[0]))
    left_waiting = [(0, i) for i in range(k)]
    right_waiting = []
    left_crossing = []
    right_crossing = []
    current_time = 0
    boxes_moved = 0

    while boxes_moved < n or right_crossing or right_waiting:
        while left_crossing and left_crossing[0][0] <= current_time:
            _, i = heapq.heappop(left_crossing)
            heapq.heappush(left_waiting, (current_time + time[i][3], i))
        
        while right_crossing and right_crossing[0][0] <= current_time:
            _, i = heapq.heappop(right_crossing)
            heapq.heappush(right_waiting, (current_time + time[i][1], i))
            boxes_moved += 1

        if right_waiting:
            _, i = heapq.heappop(right_waiting)
            heapq.heappush(left_crossing, (current_time + time[i][2], i))
            current_time += time[i][2]
        elif left_waiting and boxes_moved < n:
            _, i = heapq.heappop(left_waiting)
            heapq.heappush(right_crossing, (current_time + time[i][0], i))
            current_time += time[i][0]
        else:
            if right_crossing:
                current_time = right_crossing[0][0]
            elif left_crossing:
                current_time = left_crossing[0][0]

    return current_time
",17,False,2,65,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert findCrossingTime(1, 3, [[1,1,2,1],[1,1,3,1],[1,1,4,1]]) == 6 # ERROR: This unit test fails. Output was 3, but expected value was: 6
assert findCrossingTime(3, 2, [[1,9,1,8],[10,10,10,10]]) == 50 # ERROR: This unit test fails. Output was 24, but expected value was: 50","(False, False)",0
185,time-to-cross-a-bridge,"from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = sorted([(i, t[0] + t[2], t) for i, t in enumerate(time)], key=lambda x: (x[1], x[0]))
    left_waiting = [(0, i) for i in range(k)]
    right_waiting = []
    left_crossing = []
    right_crossing = []
    current_time = 0
    boxes_moved = 0

    while boxes_moved < n or right_crossing or right_waiting or left_crossing:
        while left_crossing and left_crossing[0][0] <= current_time:
            _, i = heapq.heappop(left_crossing)
            heapq.heappush(left_waiting, (current_time, i))
        
        while right_crossing and right_crossing[0][0] <= current_time:
            _, i = heapq.heappop(right_crossing)
            heapq.heappush(right_waiting, (current_time, i))
            boxes_moved += 1

        if right_waiting:
            _, i = heapq.heappop(right_waiting)
            current_time += time[i][2]  # Add time to cross back to the left bank
            heapq.heappush(left_crossing, (current_time + time[i][3], i))  # Add time to put down the box
        elif left_waiting and boxes_moved < n:
            _, i = heapq.heappop(left_waiting)
            current_time += time[i][0]  # Add time to cross to the right bank
            heapq.heappush(right_crossing, (current_time + time[i][1], i))  # Add time to pick up the box
        else:
            if right_crossing:
                current_time = right_crossing[0][0]
            elif left_crossing:
                current_time = left_crossing[0][0]

    return current_time",17,False,2,65,,,,1
186,check-if-point-is-reachable,"
def isReachable(targetX: int, targetY: int) -> bool:
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

    # Reduce targetX and targetY by dividing by 2 until they are odd
    while targetX % 2 == 0:
        targetX //= 2
    while targetY % 2 == 0:
        targetY //= 2

    # Check if the gcd of the reduced targetX and targetY is 1
    return gcd(targetX, targetY) == 1

# Example usage:
# print(isReachable(6, 9))  # Output: False
# print(isReachable(4, 7))  # Output: True
",17,True,60,60,40 ms,"**Tests that the code passed:**

assert isReachable(6, 9) == False
assert isReachable(4, 7) == True

**Tests that the code failed:**

No tests failed.
","(True, True)",0
187,check-if-point-is-reachable,"def isReachable(targetX: int, targetY: int) -> bool:
    """"""
    Determine if the point (targetX, targetY) can be reached from (1, 1) using a finite number of steps.
    Steps allowed:
    - (x, y - x)
    - (x - y, y)
    - (2 * x, y)
    - (x, 2 * y)
    
    Args:
    targetX (int): X-coordinate of the target point.
    targetY (int): Y-coordinate of the target point.
    
    Returns:
    bool: True if the target point can be reached, False otherwise.
    """"""
    
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

    # Early termination if the target is already the starting point
    if targetX == 1 and targetY == 1:
        return True

    # Reduce targetX and targetY by dividing by 2 until they are odd
    while targetX % 2 == 0:
        targetX //= 2
        if targetX == 1:
            return targetY % 2 == 1 or targetY == 1
    while targetY % 2 == 0:
        targetY //= 2
        if targetY == 1:
            return targetX % 2 == 1 or targetX == 1

    # Check if the gcd of the reduced targetX and targetY is 1
    return gcd(targetX, targetY) == 1

# Example usage:
# print(isReachable(6, 9))  # Output: False
# print(isReachable(4, 7))  # Output: True",17,False,-1,-1,-1,"**Tests that the code passed:**

assert isReachable(6, 9) == False
assert isReachable(4, 7) == True

**Tests that the code failed:**

No tests failed.
","(True, True)",1
188,minimum-cost-to-split-an-array,"
from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    n = nums.length
    dp = [float('inf')] * (n + 1)
    dp[0] = 0
    
    for i in range(1, n + 1):
        count = defaultdict(int)
        trimmed_length = 0
        for j in range(i, 0, -1):
            count[nums[j - 1]] += 1
            if count[nums[j - 1]] == 2:
                trimmed_length += 2
            elif count[nums[j - 1]] > 2:
                trimmed_length += 1
            dp[i] = min(dp[i], dp[j - 1] + k + trimmed_length)
    
    return dp[n]
",17,False,0,59,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.
assert minCost([1,2,1,2,1], 2) == 6 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.
assert minCost([1,2,1,2,1], 5) == 10 # ERROR: This unit test fails because the function generated: 'list' object has no attribute 'length'.","(False, False, False)",0
189,minimum-cost-to-split-an-array,"from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    n = len(nums)
    dp = [float('inf')] * (n + 1)
    dp[0] = 0
    
    for i in range(1, n + 1):
        count = defaultdict(int)
        trimmed_length = 0
        for j in range(i, 0, -1):
            count[nums[j - 1]] += 1
            if count[nums[j - 1]] == 2:
                trimmed_length += 2
            elif count[nums[j - 1]] > 2:
                trimmed_length += 1
            dp[i] = min(dp[i], dp[j - 1] + k + trimmed_length)
    
    return dp[n]",17,True,59,59,3286 ms,"**Tests that the code passed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8
assert minCost([1,2,1,2,1], 2) == 6
assert minCost([1,2,1,2,1], 5) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
190,difference-between-maximum-and-minimum-price-sum,"
from typing import List

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    from collections import defaultdict, deque
    
    # Build the adjacency list for the tree
    tree = defaultdict(list)
    for a, b in edges:
        tree[a].append(b)
        tree[b].append(a)
    
    # Function to perform DFS and calculate the maximum and minimum path sums
    def dfs(node, parent):
        max_sum = price[node]
        min_sum = price[node]
        for neighbor in tree[node]:
            if neighbor == parent:
                continue
            child_max, child_min = dfs(neighbor, node)
            max_sum = max(max_sum, price[node] + child_max)
            min_sum = min(min_sum, price[node] + child_min)
        return max_sum, min_sum
    
    max_cost = 0
    # Try rooting the tree at each node and calculate the cost
    for root in range(n):
        max_sum, min_sum = dfs(root, -1)
        max_cost = max(max_cost, max_sum - min_sum)
    
    return max_cost
",17,False,37,58,,"**Tests that the code passed:**

assert maxOutput(6, [[0,1],[1,2],[1,3],[3,4],[3,5]], [9,8,7,6,10,5]) == 24
assert maxOutput(3, [[0,1],[1,2]], [1,1,1]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
191,difference-between-maximum-and-minimum-price-sum,"from typing import List
from collections import defaultdict, deque

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    # Build the adjacency list for the tree
    tree = defaultdict(list)
    for a, b in edges:
        tree[a].append(b)
        tree[b].append(a)
    
    # Function to perform DFS and calculate the maximum and minimum path sums
    def dfs(node, parent):
        max_sum = price[node]
        min_sum = price[node]
        for neighbor in tree[node]:
            if neighbor == parent:
                continue
            child_max, child_min = dfs(neighbor, node)
            max_sum = max(max_sum, price[node] + child_max)
            min_sum = min(min_sum, price[node] + child_min)
        return max_sum, min_sum
    
    # Function to perform a single DFS traversal and gather necessary information
    def gather_info(node, parent):
        max_path_sum[node] = price[node]
        min_path_sum[node] = price[node]
        for neighbor in tree[node]:
            if neighbor == parent:
                continue
            gather_info(neighbor, node)
            max_path_sum[node] = max(max_path_sum[node], price[node] + max_path_sum[neighbor])
            min_path_sum[node] = min(min_path_sum[node], price[node] + min_path_sum[neighbor])
    
    # Initialize arrays to store the maximum and minimum path sums for each subtree
    max_path_sum = [0] * n
    min_path_sum = [0] * n
    
    # Perform a single DFS traversal to gather information
    gather_info(0, -1)
    
    # Calculate the maximum possible cost
    max_cost = 0
    for root in range(n):
        max_cost = max(max_cost, max_path_sum[root] - min_path_sum[root])
    
    return max_cost",17,False,12,58,,"**Tests that the code passed:**

assert maxOutput(6, [[0,1],[1,2],[1,3],[3,4],[3,5]], [9,8,7,6,10,5]) == 24
assert maxOutput(3, [[0,1],[1,2]], [1,1,1]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",1
192,maximize-the-minimum-powered-city,"
from typing import List

def maxPower(stations: List[int], r: int, k: int) -> int:
    def canAchieve(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        
        for i in range(len(stations)):
            if i > r:
                current_power -= additional_stations[i - r - 1]
            
            current_power += stations[i] + additional_stations[i]
            
            if current_power < min_power:
                needed = min_power - current_power
                if total_additional + needed > k:
                    return False
                additional_stations[i] += needed
                current_power += needed
                total_additional += needed
        
        return True
    
    left, right = 0, sum(stations) + k
    while left < right:
        mid = (left + right + 1) // 2
        if canAchieve(mid):
            left = mid
        else:
            right = mid - 1
    
    return left
",17,False,2,30,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert maxPower([1,2,4,5,0], 1, 2) == 5 # ERROR: This unit test fails. Output was 3, but expected value was: 5
assert maxPower([4,4,4,4], 0, 3) == 4 # ERROR: This unit test fails. Output was 7, but expected value was: 4","(False, False)",0
193,maximize-the-minimum-powered-city,"from typing import List

def maxPower(stations: List[int], r: int, k: int) -> int:
    def canAchieve(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        window_sum = sum(stations[:r+1])
        
        for i in range(len(stations)):
            if i > r:
                window_sum -= stations[i - r - 1] + additional_stations[i - r - 1]
            if i + r < len(stations):
                window_sum += stations[i + r] + additional_stations[i + r]
            
            if window_sum < min_power:
                needed = min_power - window_sum
                if total_additional + needed > k:
                    return False
                if i + r < len(stations):
                    additional_stations[i + r] += needed
                    window_sum += needed
                total_additional += needed
        
        return True
    
    left, right = 0, sum(stations) + k
    while left < right:
        mid = (left + right + 1) // 2
        if canAchieve(mid):
            left = mid
        else:
            right = mid - 1
    
    return left",17,False,4,30,,,,1
194,count-anagrams,"
from math import factorial
from collections import Counter

MOD = 10**9 + 7

def countAnagrams(s: str) -> int:
    def count_permutations(word):
        count = Counter(word)
        total_permutations = factorial(len(word))
        for freq in count.values():
            total_permutations //= factorial(freq)
        return total_permutations
    
    words = s.split()
    result = 1
    for word in words:
        result = (result * count_permutations(word)) % MOD
    
    return result
",17,False,0,41,,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
195,count-anagrams,"from math import factorial
from collections import Counter

MOD = 10**9 + 7

# Precompute factorials and modular inverses up to the maximum possible length of a word
MAX_LENGTH = 100000
factorials = [1] * (MAX_LENGTH + 1)
inv_factorials = [1] * (MAX_LENGTH + 1)

for i in range(2, MAX_LENGTH + 1):
    factorials[i] = factorials[i - 1] * i % MOD

inv_factorials[MAX_LENGTH] = pow(factorials[MAX_LENGTH], MOD - 2, MOD)
for i in range(MAX_LENGTH - 1, 0, -1):
    inv_factorials[i] = inv_factorials[i + 1] * (i + 1) % MOD

def countAnagrams(s: str) -> int:
    def count_permutations(word):
        count = Counter(word)
        total_permutations = factorials[len(word)]
        for freq in count.values():
            total_permutations = total_permutations * inv_factorials[freq] % MOD
        return total_permutations
    
    words = s.split()
    result = 1
    for word in words:
        result = (result * count_permutations(word)) % MOD
    
    return result",17,False,0,41,,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
196,number-of-great-partitions,"
from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    dp = [0] * (total_sum + 1)
    dp[0] = 1
    
    for num in nums:
        for j in range(total_sum, num - 1, -1):
            dp[j] = (dp[j] + dp[j - num]) % MOD
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp[i]) % MOD
    
    return result
",17,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
197,number-of-great-partitions,"from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    # Use a rolling array to reduce space complexity
    dp = [0] * (total_sum + 1)
    dp[0] = 1
    
    for num in nums:
        for j in range(total_sum, num - 1, -1):
            dp[j] = (dp[j] + dp[j - num]) % MOD
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp[i]) % MOD
    
    return result",17,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
198,cycle-length-queries-in-a-tree,"
from typing import List

def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node):
        path = []
        while node > 0:
            path.append(node)
            node //= 2
        return path

    def find_cycle_length(a, b):
        path_a = find_path_to_root(a)
        path_b = find_path_to_root(b)
        i, j = len(path_a) - 1, len(path_b) - 1
        
        while i >= 0 and j >= 0 and path_a[i] == path_b[j]:
            i -= 1
            j -= 1
        
        return (i + 1) + (j + 1) + 1

    result = []
    for a, b in queries:
        result.append(find_cycle_length(a, b))
    
    return result
",17,True,27,27,946 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
199,cycle-length-queries-in-a-tree,"from typing import List

def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node, cache):
        if node in cache:
            return cache[node]
        path = []
        current = node
        while current > 0:
            path.append(current)
            current //= 2
        cache[node] = path
        return path

    def find_cycle_length(a, b, cache):
        path_a = find_path_to_root(a, cache)
        path_b = find_path_to_root(b, cache)
        i, j = len(path_a) - 1, len(path_b) - 1
        
        while i >= 0 and j >= 0 and path_a[i] == path_b[j]:
            i -= 1
            j -= 1
        
        return (i + 1) + (j + 1) + 1

    result = []
    cache = {}
    for a, b in queries:
        result.append(find_cycle_length(a, b, cache))
    
    return result",17,True,27,27,1203 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
200,add-edges-to-make-degrees-of-all-nodes-even,"
def isPossible(n: int, edges: List[List[int]]) -> bool:
    from collections import defaultdict

    # Calculate the degree of each node
    degree = [0] * (n + 1)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    # Find all nodes with odd degree
    odd_degree_nodes = [i for i in range(1, n + 1) if degree[i] % 2 == 1]

    # If there are no odd degree nodes, the graph is already valid
    if len(odd_degree_nodes) == 0:
        return True

    # If there are more than 4 odd degree nodes, it's impossible to fix with at most 2 edges
    if len(odd_degree_nodes) > 4:
        return False

    # If there are exactly 2 odd degree nodes, we can try to connect them directly or through another node
    if len(odd_degree_nodes) == 2:
        u, v = odd_degree_nodes
        if [u, v] not in edges and [v, u] not in edges:
            return True
        for i in range(1, n + 1):
            if i != u and i != v and [u, i] not in edges and [i, u] not in edges and [v, i] not in edges and [i, v] not in edges:
                return True
        return False

    # If there are exactly 4 odd degree nodes, we need to check if we can pair them up
    if len(odd_degree_nodes) == 4:
        u, v, w, x = odd_degree_nodes
        pairs = [(u, v), (u, w), (u, x), (v, w), (v, x), (w, x)]
        for (a, b) in pairs:
            remaining = [node for node in odd_degree_nodes if node not in (a, b)]
            c, d = remaining
            if ([a, b] not in edges and [b, a] not in edges) and ([c, d] not in edges and [d, c] not in edges):
                return True
        return False

    return False
",17,False,46,52,,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True
assert isPossible(4, [[1,2],[1,3],[1,4]]) == False

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
201,add-edges-to-make-degrees-of-all-nodes-even,"def isPossible(n: int, edges: List[List[int]]) -> bool:
    from collections import defaultdict, deque

    # Convert edges list to a set of tuples for faster lookups
    edge_set = set(tuple(edge) for edge in edges)

    # Calculate the degree of each node
    degree = [0] * (n + 1)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    # Find all nodes with odd degree
    odd_degree_nodes = [i for i in range(1, n + 1) if degree[i] % 2 == 1]

    # If there are no odd degree nodes, the graph is already valid
    if len(odd_degree_nodes) == 0:
        return True

    # If there are more than 4 odd degree nodes, it's impossible to fix with at most 2 edges
    if len(odd_degree_nodes) > 4:
        return False

    # Helper function to check if two nodes can be connected
    def can_connect(u, v):
        return (u, v) not in edge_set and (v, u) not in edge_set

    # If there are exactly 2 odd degree nodes, we can try to connect them directly or through another node
    if len(odd_degree_nodes) == 2:
        u, v = odd_degree_nodes
        if can_connect(u, v):
            return True
        for i in range(1, n + 1):
            if i != u and i != v and can_connect(u, i) and can_connect(v, i):
                return True
        return False

    # If there are exactly 4 odd degree nodes, we need to check if we can pair them up
    if len(odd_degree_nodes) == 4:
        u, v, w, x = odd_degree_nodes
        pairs = [(u, v), (u, w), (u, x), (v, w), (v, x), (w, x)]
        for (a, b) in pairs:
            remaining = [node for node in odd_degree_nodes if node not in (a, b)]
            c, d = remaining
            if can_connect(a, b) and can_connect(c, d):
                return True
        return False

    return False",17,True,52,52,1220 ms,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True
assert isPossible(4, [[1,2],[1,3],[1,4]]) == False

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
202,minimum-total-cost-to-make-arrays-unequal,"
from typing import List

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    swaps = []
    
    for i in range(n):
        if nums1[i] == nums2[i]:
            swaps.append(i)
    
    if not swaps:
        return 0
    
    swaps.sort()
    total_cost = 0
    
    for i in range(len(swaps)):
        if i == 0:
            total_cost += swaps[i]
        else:
            total_cost += swaps[i] + swaps[i-1]
    
    return total_cost if total_cost > 0 else -1
",17,False,7,104,,"**Tests that the code passed:**

assert minimumTotalCost([2,2,2,1,3], [1,2,2,3,3]) == 10

**Tests that the code failed:**

assert minimumTotalCost([1,2,3,4,5], [1,2,3,4,5]) == 10 # ERROR: This unit test fails. Output was 16, but expected value was: 10
assert minimumTotalCost([1,2,2], [1,2,2]) == -1 # ERROR: This unit test fails. Output was 4, but expected value was: -1","(False, True, False)",0
203,minimum-total-cost-to-make-arrays-unequal,"from typing import List
from collections import Counter

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    if n == 0:
        return -1
    
    # Check if nums1 and nums2 are already different
    for i in range(n):
        if nums1[i] == nums2[i]:
            break
    else:
        return 0
    
    # Count the frequency of each element in nums1 and nums2
    count1 = Counter(nums1)
    count2 = Counter(nums2)
    
    # Check if it's possible to make nums1 different from nums2
    for num in count1:
        if count1[num] + count2[num] > n:
            return -1
    
    # Find the minimum cost to make nums1 different from nums2
    swaps = []
    for i in range(n):
        if nums1[i] == nums2[i]:
            swaps.append(i)
    
    if not swaps:
        return 0
    
    total_cost = 0
    used_indices = set()
    
    def find_swap_index(i):
        for j in range(n):
            if j not in used_indices and nums1[j] != nums2[j] and nums1[i] != nums2[j] and nums1[j] != nums2[i]:
                return j
        return -1
    
    for i in swaps:
        j = find_swap_index(i)
        if j == -1:
            return -1
        nums1[i], nums1[j] = nums1[j], nums1[i]
        total_cost += i + j
        used_indices.add(i)
        used_indices.add(j)
    
    # Final check to ensure all elements are different
    for i in range(n):
        if nums1[i] == nums2[i]:
            return -1
    
    return total_cost",17,False,25,104,,,,1
204,maximum-number-of-points-from-grid-queries,"
from typing import List
from collections import deque

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    def bfs(query):
        m, n = len(grid), len(grid[0])
        visited = [[False] * n for _ in range(m)]
        queue = deque([(0, 0)])
        points = 0
        
        while queue:
            x, y = queue.popleft()
            if visited[x][y]:
                continue
            visited[x][y] = True
            
            if grid[x][y] < query:
                points += 1
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < m and 0 <= ny < n and not visited[nx][ny]:
                        queue.append((nx, ny))
        
        return points
    
    return [bfs(query) for query in queries]
",17,False,17,21,,"**Tests that the code passed:**

assert maxPoints([[1,2,3],[2,5,7],[3,5,1]], [5,6,2]) == [5,8,1]
assert maxPoints([[5,2,1],[1,1,2]], [3]) == [0]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
205,maximum-number-of-points-from-grid-queries,"from typing import List
from collections import deque
import heapq

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    def bfs(query):
        m, n = len(grid), len(grid[0])
        visited = [[False] * n for _ in range(m)]
        queue = [(grid[0][0], 0, 0)]
        points = 0
        
        while queue:
            value, x, y = heapq.heappop(queue)
            if visited[x][y]:
                continue
            visited[x][y] = True
            
            if value < query:
                points += 1
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < m and 0 <= ny < n and not visited[nx][ny]:
                        heapq.heappush(queue, (grid[nx][ny], nx, ny))
            else:
                break
        
        return points
    
    return [bfs(query) for query in queries]",17,False,17,21,,"**Tests that the code passed:**

assert maxPoints([[1,2,3],[2,5,7],[3,5,1]], [5,6,2]) == [5,8,1]
assert maxPoints([[5,2,1],[1,1,2]], [3]) == [0]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
206,divide-nodes-into-the-maximum-number-of-groups,"
from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1
        
        return max_depth + 1

    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    visited_global = set()
    max_groups = 0
    
    for node in range(1, n + 1):
        if node not in visited_global:
            result = bfs(node)
            if result == -1:
                return -1
            max_groups += result
            visited_global.update(graph[node])
    
    return max_groups
",17,False,11,55,,"**Tests that the code passed:**

assert magnificentSets(3, [[1,2],[2,3],[3,1]]) == -1

**Tests that the code failed:**

assert magnificentSets(6, [[1,2],[1,4],[1,5],[2,6],[2,3],[4,6]]) == 4 # ERROR: This unit test fails. Output was 11, but expected value was: 4","(False, True)",0
207,divide-nodes-into-the-maximum-number-of-groups,"from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1, visited
        
        return max_depth + 1, visited

    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    visited_global = set()
    max_groups = 0
    
    for node in range(1, n + 1):
        if node not in visited_global:
            result, visited = bfs(node)
            if result == -1:
                return -1
            max_groups = max(max_groups, result)
            visited_global.update(visited.keys())
    
    return max_groups",17,False,23,55,,,,1
208,count-palindromic-subsequences,"
def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Base case: single characters are palindromes of length 1
    for i in range(n):
        dp[i][i][1] = 1
    
    # Fill the dp table
    for length in range(2, n + 1):  # length of the substring
        for i in range(n - length + 1):
            j = i + length - 1
            for k in range(1, 6):
                if s[i] == s[j]:
                    if k == 1:
                        dp[i][j][k] = 2
                    else:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
                else:
                    dp[i][j][k] = (dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
    
    # Sum up all palindromic subsequences of length 5
    result = 0
    for i in range(n):
        for j in range(i, n):
            result = (result + dp[i][j][5]) % MOD
    
    return result
",17,False,0,63,,"**Tests that the code passed:**

assert countPalindromes(""103301"") == 2

**Tests that the code failed:**

assert countPalindromes(""0000000"") == 21 # ERROR: This unit test fails. Output was 35, but expected value was: 21
assert countPalindromes(""9999900000"") == 2 # ERROR: This unit test fails. Output was 12, but expected value was: 2","(True, False, False)",0
209,count-palindromic-subsequences,"def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Base case: single characters are palindromes of length 1
    for i in range(n):
        dp[i][i][1] = 1
    
    # Fill the dp table
    for length in range(2, n + 1):  # length of the substring
        for i in range(n - length + 1):
            j = i + length - 1
            for k in range(1, 6):
                if s[i] == s[j]:
                    if k == 2:
                        dp[i][j][k] = 1 if length == 2 else 0
                    elif k > 2:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
                else:
                    dp[i][j][k] = (dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
    
    # Sum up all palindromic subsequences of length 5
    result = 0
    for i in range(n):
        for j in range(i, n):
            result = (result + dp[i][j][5]) % MOD
    
    return result",17,False,0,63,,,,1
210,count-subarrays-with-median-k,"
def countSubarrays(nums: List[int], k: int) -> int:
    from collections import defaultdict

    def count_medians(arr, k):
        balance = 0
        count = 0
        prefix_counts = defaultdict(int)
        prefix_counts[0] = 1

        for num in arr:
            if num < k:
                balance -= 1
            elif num > k:
                balance += 1
            else:
                count += prefix_counts[balance] + prefix_counts[balance - 1]
                balance += 1

            prefix_counts[balance] += 1

        return count

    return count_medians(nums, k)
",17,False,12,45,,"**Tests that the code passed:**

assert countSubarrays([2,3,1], 3) == 1

**Tests that the code failed:**

assert countSubarrays([3,2,1,4,5], 4) == 3 # ERROR: This unit test fails. Output was 1, but expected value was: 3","(False, True)",0
211,count-subarrays-with-median-k,"def countSubarrays(nums: List[int], k: int) -> int:
    from collections import defaultdict

    def count_medians(arr, k):
        balance = 0
        count = 0
        prefix_counts = defaultdict(int)
        prefix_counts[0] = 1
        found_k = False

        for num in arr:
            if num < k:
                balance -= 1
            elif num > k:
                balance += 1
            else:
                found_k = True

            if found_k:
                count += prefix_counts[balance] + prefix_counts[balance - 1]
            else:
                prefix_counts[balance] += 1

        return count

    return count_medians(nums, k)",17,True,45,45,350 ms,"**Tests that the code passed:**

assert countSubarrays([3,2,1,4,5], 4) == 3
assert countSubarrays([2,3,1], 3) == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
212,number-of-beautiful-partitions,"
def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    n = len(s)
    
    def is_prime_digit(c):
        return c in prime_digits
    
    def is_non_prime_digit(c):
        return c not in prime_digits
    
    # dp[i][j] will be the number of ways to partition the first i characters into j parts
    dp = [[0] * (k + 1) for _ in range(n + 1)]
    dp[0][0] = 1
    
    for i in range(1, n + 1):
        for j in range(1, k + 1):
            for l in range(minLength, i + 1):
                if is_prime_digit(s[i - l]) and is_non_prime_digit(s[i - 1]):
                    dp[i][j] = (dp[i][j] + dp[i - l][j - 1]) % MOD
    
    return dp[n][k]
",17,False,36,74,,"**Tests that the code passed:**

assert beautifulPartitions(""23542185131"", 3, 2) == 3
assert beautifulPartitions(""23542185131"", 3, 3) == 1
assert beautifulPartitions(""3312958"", 3, 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
213,number-of-beautiful-partitions,"def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    n = len(s)
    
    def is_prime_digit(c):
        return c in prime_digits
    
    def is_non_prime_digit(c):
        return c not in prime_digits
    
    # Edge case handling
    if k == 1:
        return 1 if is_prime_digit(s[0]) and is_non_prime_digit(s[-1]) and len(s) >= minLength else 0
    if all(is_prime_digit(c) for c in s) or all(is_non_prime_digit(c) for c in s):
        return 0
    if k > n // minLength:
        return 0
    
    # dp[i][j] will be the number of ways to partition the first i characters into j parts
    dp = [[0] * (k + 1) for _ in range(n + 1)]
    dp[0][0] = 1
    
    # Prefix sums to optimize the inner loop
    prefix_sums = [[0] * (k + 1) for _ in range(n + 1)]
    for j in range(k + 1):
        prefix_sums[0][j] = dp[0][j]
    
    for i in range(1, n + 1):
        for j in range(1, k + 1):
            if i >= minLength:
                for start in range(max(0, i - minLength + 1), i):
                    if is_prime_digit(s[start]) and is_non_prime_digit(s[i - 1]):
                        dp[i][j] = (dp[i][j] + dp[start][j - 1]) % MOD
            prefix_sums[i][j] = (prefix_sums[i - 1][j] + dp[i][j]) % MOD
    
    return dp[n][k] % MOD",17,False,21,74,,,,1
214,split-message-based-on-limit,"
from typing import List

def splitMessage(message: str, limit: int) -> List[str]:
    def calculate_parts_count(message_length, limit):
        for parts in range(1, message_length + 1):
            suffix_length = len(f""<{parts}/{parts}>"")
            if parts * (limit - suffix_length) >= message_length:
                return parts
        return -1

    message_length = len(message)
    parts_count = calculate_parts_count(message_length, limit)
    
    if parts_count == -1:
        return []

    result = []
    index = 0
    for part in range(1, parts_count + 1):
        suffix = f""<{part}/{parts_count}>""
        part_length = limit - len(suffix)
        result.append(message[index:index + part_length] + suffix)
        index += part_length

    return result
",17,False,71,94,,"**Tests that the code passed:**

assert splitMessage(""short message"", 15) == [""short mess<1/2>"",""age<2/2>""]

**Tests that the code failed:**

assert splitMessage(""this is really a very awesome message"", 9) == [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""] # ERROR: This unit test fails. Output was ['thi<1/19>', 's i<2/19>', 's r<3/19>', 'eal<4/19>', 'ly <5/19>', 'a v<6/19>', 'ery<7/19>', ' aw<8/19>', 'eso<9/19>', 'me<10/19>', ' m<11/19>', 'es<12/19>', 'sa<13/19>', 'ge<14/19>', '<15/19>', '<16/19>', '<17/19>', '<18/19>', '<19/19>'], but expected value was: [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""]","(False, True)",0
215,split-message-based-on-limit,"from typing import List

def splitMessage(message: str, limit: int) -> List[str]:
    def calculate_parts_count(message_length, limit):
        def suffix_length(parts):
            return len(f""<{1}/{parts}>"")
        
        low, high = 1, message_length
        while low < high:
            mid = (low + high) // 2
            if mid * (limit - suffix_length(mid)) >= message_length + mid * suffix_length(mid):
                high = mid
            else:
                low = mid + 1
        return low if low * (limit - suffix_length(low)) >= message_length + low * suffix_length(low) else -1

    message_length = len(message)
    parts_count = calculate_parts_count(message_length, limit)
    
    if parts_count == -1:
        return []

    result = []
    index = 0
    for part in range(1, parts_count + 1):
        suffix = f""<{part}/{parts_count}>""
        part_length = limit - len(suffix)
        if index + part_length > message_length:
            part_length = message_length - index
        if part_length > 0:
            result.append(message[index:index + part_length] + suffix)
        index += part_length

    return result",17,False,52,94,,,,1
216,maximum-number-of-non-overlapping-palindrome-substrings,"
def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    dp = [0] * (n + 1)

    for i in range(k, n + 1):
        for j in range(i - k + 1):
            if is_palindrome(s[j:i]):
                dp[i] = max(dp[i], dp[j] + 1)
    
    return max(dp)
",17,False,12,56,,"**Tests that the code passed:**

assert maxPalindromes(""adbcda"", 2) == 0

**Tests that the code failed:**

assert maxPalindromes(""abaccdbbd"", 3) == 2 # ERROR: This unit test fails. Output was 1, but expected value was: 2","(False, True)",0
217,maximum-number-of-non-overlapping-palindrome-substrings,"def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    dp = [0] * (n + 1)
    last_palindrome_end = [-1] * (n + 1)

    # Precompute palindrome substrings using dynamic programming
    is_pal = [[False] * n for _ in range(n)]
    for i in range(n):
        is_pal[i][i] = True
    for i in range(n - 1):
        is_pal[i][i + 1] = (s[i] == s[i + 1])
    for length in range(3, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            is_pal[i][j] = (s[i] == s[j]) and is_pal[i + 1][j - 1]

    for i in range(k, n + 1):
        for j in range(i - k + 1):
            if is_pal[j][i - 1] and (j > last_palindrome_end[i - k]):
                dp[i] = max(dp[i], dp[j] + 1)
        last_palindrome_end[i] = i - 1

    return max(dp)",17,False,22,56,,,,1
218,minimum-total-distance-traveled,"
from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort()
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for i in range(n + 1):
        for j in range(m):
            dp[i][j + 1] = min(dp[i][j + 1], dp[i][j])
            total_distance = 0
            for k in range(1, min(factory[j][1], n - i) + 1):
                total_distance += abs(robot[i + k - 1] - factory[j][0])
                dp[i + k][j + 1] = min(dp[i + k][j + 1], dp[i][j] + total_distance)
    
    return dp[n][m]
",17,False,0,40,,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
219,minimum-total-distance-traveled,"from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort()
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for robot_index in range(n + 1):
        for factory_index in range(m):
            dp[robot_index][factory_index + 1] = min(dp[robot_index][factory_index + 1], dp[robot_index][factory_index])
            total_distance = 0
            for k in range(1, min(factory[factory_index][1], n - robot_index) + 1):
                total_distance += abs(robot[robot_index + k - 1] - factory[factory_index][0])
                dp[robot_index + k][factory_index + 1] = min(dp[robot_index + k][factory_index + 1], dp[robot_index][factory_index] + total_distance)
    
    return dp[n][m]",17,True,40,40,822 ms,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",1
220,next-greater-element-iv,"
from typing import List

def secondGreaterElement(nums: List[int]) -> List[int]:
    n = len(nums)
    answer = [-1] * n
    first_greater = [-1] * n
    stack = []

    # Find the first greater element for each index
    for i in range(n):
        while stack and nums[stack[-1]] < nums[i]:
            first_greater[stack.pop()] = i
        stack.append(i)

    # Clear the stack to reuse it for finding the second greater element
    stack = []

    # Find the second greater element for each index
    for i in range(n):
        while stack and nums[stack[-1]] < nums[i]:
            idx = stack.pop()
            if first_greater[idx] != -1 and first_greater[idx] < i:
                answer[idx] = nums[i]
        stack.append(i)

    return answer
",17,False,-1,-1,-1,"**Tests that the code passed:**

assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1] # ERROR: This unit test fails. Output was [-1, -1, -1, -1, -1], but expected value was: [9,6,6,-1,-1]","(False, True)",0
221,next-greater-element-iv,"from typing import List
from collections import deque

def secondGreaterElement(nums: List[int]) -> List[int]:
    n = len(nums)
    answer = [-1] * n
    first_greater = [-1] * n
    stack = []

    # Find the first greater element for each index
    for i in range(n):
        while stack and nums[stack[-1]] < nums[i]:
            first_greater[stack.pop()] = i
        stack.append(i)

    # Clear the stack to reuse it for finding the second greater element
    stack = []

    # Use a deque to keep track of potential candidates for the second greater element
    candidates = deque()

    # Find the second greater element for each index
    for i in range(n):
        while candidates and nums[candidates[-1]] < nums[i]:
            idx = candidates.pop()
            if first_greater[idx] != -1 and first_greater[idx] < i:
                answer[idx] = nums[i]
        while stack and nums[stack[-1]] < nums[i]:
            candidates.append(stack.pop())
        stack.append(i)

    return answer",17,False,-1,-1,-1,"**Tests that the code passed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1]
assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
222,minimum-number-of-operations-to-make-arrays-similar,"
from typing import List

def makeSimilar(nums: List[int], target: List[int]) -> int:
    nums.sort()
    target.sort()
    
    odd_nums = [x for x in nums if x % 2 != 0]
    even_nums = [x for x in nums if x % 2 == 0]
    odd_target = [x for x in target if x % 2 != 0]
    even_target = [x for x in target if x % 2 == 0]
    
    operations = 0
    
    for i in range(len(odd_nums)):
        operations += abs(odd_nums[i] - odd_target[i]) // 2
    
    for i in range(len(even_nums)):
        operations += abs(even_nums[i] - even_target[i]) // 2
    
    return operations // 2
",17,True,32,32,717 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
223,minimum-number-of-operations-to-make-arrays-similar,"from typing import List

def makeSimilar(nums: List[int], target: List[int]) -> int:
    nums.sort()
    target.sort()
    
    odd_nums = [x for x in nums if x % 2 != 0]
    even_nums = [x for x in nums if x % 2 == 0]
    odd_target = [x for x in target if x % 2 != 0]
    even_target = [x for x in target if x % 2 == 0]
    
    operations = 0
    
    def calculate_operations(source: List[int], target: List[int]) -> int:
        ops = 0
        for i in range(len(source)):
            ops += abs(source[i] - target[i]) // 2
        return ops
    
    operations += calculate_operations(odd_nums, odd_target)
    operations += calculate_operations(even_nums, even_target)
    
    return operations // 2",17,True,32,32,722 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
224,minimum-cost-to-make-array-equal,"
from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    left, right = min(nums), max(nums)
    while left < right:
        mid = (left + right) // 2
        cost1 = get_cost(mid)
        cost2 = get_cost(mid + 1)
        
        if cost1 < cost2:
            right = mid
        else:
            left = mid + 1
    
    return get_cost(left)
",17,True,48,48,595 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
225,minimum-cost-to-make-array-equal,"from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    left, right = min(nums), max(nums)
    cost_cache = {}
    
    while left < right:
        mid = (left + right) // 2
        if mid not in cost_cache:
            cost_cache[mid] = get_cost(mid)
        if mid + 1 not in cost_cache:
            cost_cache[mid + 1] = get_cost(mid + 1)
        
        cost1 = cost_cache[mid]
        cost2 = cost_cache[mid + 1]
        
        if cost1 < cost2:
            right = mid
        else:
            left = mid + 1
    
    return get_cost(left)",17,True,48,48,607 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
226,create-components-with-same-value,"
from typing import List
from collections import defaultdict, deque

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = [False] * n
        def dfs(node):
            visited[node] = True
            total = nums[node]
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total > target:
                return -1
            return 0 if total == target else total
        
        for i in range(n):
            if not visited[i]:
                if dfs(i) != 0:
                    return False
        return True

    n = len(nums)
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    for k in range(n, 0, -1):
        if total_sum % k == 0:
            if can_split(total_sum // k):
                return k - 1
    return 0
",17,True,43,43,1123 ms,"**Tests that the code passed:**

assert componentValue([6,2,2,2,6], [[0,1],[1,2],[1,3],[3,4]]) == 2
assert componentValue([2], []) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
227,create-components-with-same-value,"from typing import List
from collections import defaultdict, deque

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = [False] * n
        def dfs(node):
            visited[node] = True
            total = nums[node]
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total > target:
                return -1
            return 0 if total == target else total
        
        for i in range(n):
            if not visited[i]:
                if dfs(i) != 0:
                    return False
        return True

    def is_prime(num):
        if num <= 1:
            return False
        for i in range(2, int(num**0.5) + 1):
            if num % i == 0:
                return False
        return True

    n = len(nums)
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    
    if is_prime(total_sum):
        return 0
    
    for k in range(n, 0, -1):
        if total_sum % k == 0:
            if can_split(total_sum // k):
                return k - 1
    return 0",17,True,43,43,1137 ms,"**Tests that the code passed:**

assert componentValue([6,2,2,2,6], [[0,1],[1,2],[1,3],[3,4]]) == 2
assert componentValue([2], []) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
228,count-subarrays-with-fixed-bounds,"
from typing import List

def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = left_bound = -1
    
    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            left_bound = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        count += max(0, min(min_pos, max_pos) - left_bound)
    
    return count
",17,True,52,52,710 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",0
229,count-subarrays-with-fixed-bounds,"from typing import List

def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = last_invalid_index = -1
    
    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            last_invalid_index = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        if min_pos != -1 and max_pos != -1:
            count += max(0, min(min_pos, max_pos) - last_invalid_index)
    
    return count",17,True,52,52,681 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",1
230,longest-increasing-subsequence-ii,"
from typing import List
from bisect import bisect_left

def lengthOfLIS(nums: List[int], k: int) -> int:
    dp = []
    
    for num in nums:
        pos = bisect_left(dp, num)
        if pos < len(dp):
            if pos == 0 or num - dp[pos - 1] <= k:
                dp[pos] = num
        else:
            if pos == 0 or num - dp[-1] <= k:
                dp.append(num)
    
    return len(dp)
",17,False,76,84,,"**Tests that the code passed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4
assert lengthOfLIS([1,5], 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
231,longest-increasing-subsequence-ii,"from typing import List
from bisect import bisect_left

def lengthOfLIS(nums: List[int], k: int) -> int:
    dp = []
    
    for num in nums:
        pos = bisect_left(dp, num)
        if pos < len(dp):
            if pos == 0 or (num - dp[pos - 1] <= k and (pos == len(dp) - 1 or dp[pos + 1] - num > k)):
                dp[pos] = num
        else:
            if pos == 0 or num - dp[-1] <= k:
                dp.append(num)
    
    return len(dp)",17,False,-1,-1,-1,"**Tests that the code passed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4
assert lengthOfLIS([1,5], 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
232,paths-in-matrix-whose-sum-is-divisible-by-k,"
from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # dp[i][j][r] will store the number of ways to reach cell (i, j) with a sum % k == r
    dp = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp[i][j][r] > 0:
                    if i + 1 < m:
                        new_r = (r + grid[i + 1][j]) % k
                        dp[i + 1][j][new_r] = (dp[i + 1][j][new_r] + dp[i][j][r]) % MOD
                    if j + 1 < n:
                        new_r = (r + grid[i][j + 1]) % k
                        dp[i][j + 1][new_r] = (dp[i][j + 1][new_r] + dp[i][j][r]) % MOD
    
    return dp[m - 1][n - 1][0]
",17,True,88,88,2216 ms,"**Tests that the code passed:**

assert numberOfPaths([[5,2,4],[3,0,5],[0,7,2]], 3) == 2
assert numberOfPaths([[0,0]], 5) == 1
assert numberOfPaths([[7,3,4,9],[2,3,6,2],[2,3,7,0]], 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
233,paths-in-matrix-whose-sum-is-divisible-by-k,"from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # Using a 3D DP array to retain values across rows
    dp = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp[i][j][r] > 0:
                    if i + 1 < m:
                        new_r = (r + grid[i + 1][j]) % k
                        dp[i + 1][j][new_r] = (dp[i + 1][j][new_r] + dp[i][j][r]) % MOD
                    if j + 1 < n:
                        new_r = (r + grid[i][j + 1]) % k
                        dp[i][j + 1][new_r] = (dp[i][j + 1][new_r] + dp[i][j][r]) % MOD
    
    return dp[m - 1][n - 1][0] % MOD",17,True,88,88,2211 ms,,,1
234,minimum-time-to-visit-a-cell-in-a-grid,"
from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    visited = set()

    while pq:
        time, row, col = heapq.heappop(pq)
        if (row, col) in visited:
            continue
        visited.add((row, col))
        
        if row == m - 1 and col == n - 1:
            return time
        
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n and (new_row, new_col) not in visited:
                new_time = max(time + 1, grid[new_row][new_col])
                heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1
",21,False,22,41,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert minimumTime([[0,1,3,2],[5,1,2,5],[4,3,8,6]]) == 7 # ERROR: This unit test fails. Output was 6, but expected value was: 7
assert minimumTime([[0,2,4],[3,2,1],[1,0,4]]) == -1 # ERROR: This unit test fails. Output was 5, but expected value was: -1","(False, False)",0
235,minimum-time-to-visit-a-cell-in-a-grid,"from typing import List
import heapq

def minimumTime(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
    pq = [(0, 0, 0)]  # (time, row, col)
    visited = set()

    while pq:
        time, row, col = heapq.heappop(pq)
        if (row, col) in visited:
            continue
        visited.add((row, col))
        
        if row == m - 1 and col == n - 1:
            return time
        
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < m and 0 <= new_col < n and (new_row, new_col) not in visited:
                new_time = time + 1
                if new_time >= grid[new_row][new_col]:
                    heapq.heappush(pq, (new_time, new_row, new_col))
    
    return -1",21,False,8,41,,"**Tests that the code passed:**

assert minimumTime([[0,1,3,2],[5,1,2,5],[4,3,8,6]]) == 7
assert minimumTime([[0,2,4],[3,2,1],[1,0,4]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
236,find-the-string-with-lcp,"
def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    word = [''] * n
    current_char = 'a'
    
    for i in range(n):
        if word[i] == '':
            word[i] = current_char
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    word[j] = word[i]
            current_char = chr(ord(current_char) + 1)
    
    for i in range(n):
        for j in range(n):
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    return ''.join(word)
",21,False,36,55,,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
237,find-the-string-with-lcp,"from typing import List

def findTheString(lcp: List[List[int]]) -> str:
    n = len(lcp)
    if n == 1:
        return 'a'
    
    word = [''] * n
    current_char = 'a'
    
    for i in range(n):
        if word[i] == '':
            word[i] = current_char
            for j in range(i + 1, n):
                if lcp[i][j] > 0:
                    word[j] = word[i]
            current_char = chr(ord(current_char) + 1)
            if current_char > 'z':
                return """"
    
    for i in range(n):
        for j in range(n):
            if lcp[i][j] != lcp[j][i]:
                return """"
            if lcp[i][j] != 0:
                if word[i] != word[j]:
                    return """"
                k = 0
                while i + k < n and j + k < n and word[i + k] == word[j + k]:
                    k += 1
                if k != lcp[i][j]:
                    return """"
            else:
                if i != j and word[i] == word[j]:
                    return """"
    
    return ''.join(word)",21,False,35,55,,"**Tests that the code passed:**

assert findTheString([[4,0,2,0],[0,3,0,1],[2,0,2,0],[0,1,0,1]]) == ""abab""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,1]]) == ""aaaa""
assert findTheString([[4,3,2,1],[3,3,2,1],[2,2,2,1],[1,1,1,3]]) == """"

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
238,handling-sum-queries-after-update,"
from typing import List

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    result = []
    
    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            for i in range(l, r + 1):
                nums1[i] = 1 - nums1[i]
        elif query[0] == 2:
            p = query[1]
            for i in range(len(nums2)):
                nums2[i] += nums1[i] * p
        elif query[0] == 3:
            result.append(sum(nums2))
    
    return result
",21,False,72,77,,"**Tests that the code passed:**

assert handleQuery([1,0,1], [0,0,0], [[1,1,1],[2,1,0],[3,0,0]]) == [3]
assert handleQuery([1], [5], [[2,0,0],[3,0,0]]) == [5]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
239,handling-sum-queries-after-update,"from typing import List

class SegmentTree:
    def __init__(self, n):
        self.n = n
        self.tree = [0] * (4 * n)
        self.lazy = [0] * (4 * n)

    def update_range(self, start, end, l, r, node):
        if self.lazy[node] != 0:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > end or start > r or end < l:
            return

        if start >= l and end <= r:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            return

        mid = (start + end) // 2
        self.update_range(start, mid, l, r, node * 2 + 1)
        self.update_range(mid + 1, end, l, r, node * 2 + 2)
        self.tree[node] = self.tree[node * 2 + 1] + self.tree[node * 2 + 2]

    def query_range(self, start, end, l, r, node):
        if self.lazy[node] != 0:
            self.tree[node] = (end - start + 1) - self.tree[node]
            if start != end:
                self.lazy[node * 2 + 1] ^= 1
                self.lazy[node * 2 + 2] ^= 1
            self.lazy[node] = 0

        if start > end or start > r or end < l:
            return 0

        if start >= l and end <= r:
            return self.tree[node]

        mid = (start + end) // 2
        left_query = self.query_range(start, mid, l, r, node * 2 + 1)
        right_query = self.query_range(mid + 1, end, l, r, node * 2 + 2)
        return left_query + right_query

def handleQuery(nums1: List[int], nums2: List[int], queries: List[List[int]]) -> List[int]:
    n = len(nums1)
    seg_tree = SegmentTree(n)
    result = []
    sum_nums2 = sum(nums2)
    current_nums1 = nums1[:]

    for query in queries:
        if query[0] == 1:
            l, r = query[1], query[2]
            seg_tree.update_range(0, n - 1, l, r, 0)
            for i in range(l, r + 1):
                current_nums1[i] = 1 - current_nums1[i]
        elif query[0] == 2:
            p = query[1]
            for i in range(n):
                if current_nums1[i] == 1:
                    nums2[i] += p
            sum_nums2 = sum(nums2)
        elif query[0] == 3:
            result.append(sum_nums2)

    return result",21,False,0,77,,"**Tests that the code passed:**

assert handleQuery([1,0,1], [0,0,0], [[1,1,1],[2,1,0],[3,0,0]]) == [3]
assert handleQuery([1], [5], [[2,0,0],[3,0,0]]) == [5]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
240,subsequence-with-the-minimum-score,"
def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Calculate the leftmost positions where t can be a subsequence of s
    left = [-1] * m
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            left[j] = i
            j += 1
    
    # Calculate the rightmost positions where t can be a subsequence of s
    right = [-1] * m
    j = m - 1
    for i in range(n - 1, -1, -1):
        if j >= 0 and s[i] == t[j]:
            right[j] = i
            j -= 1
    
    # If t is already a subsequence of s without removing any characters
    if left[-1] != -1:
        return 0
    
    # Find the minimum score by removing characters from t
    min_score = float('inf')
    for i in range(m):
        if left[i] != -1:
            # Find the first position in right that is greater than left[i]
            j = i + 1
            while j < m and (right[j] == -1 or right[j] <= left[i]):
                j += 1
            if j < m:
                min_score = min(min_score, j - i)
    
    return min_score if min_score != float('inf') else m
",21,False,14,54,,"**Tests that the code passed:**

assert minimumScore(""cde"", ""xyz"") == 3

**Tests that the code failed:**

assert minimumScore(""abacaba"", ""bzaa"") == 1 # ERROR: This unit test fails. Output was 2, but expected value was: 1","(False, True)",0
241,subsequence-with-the-minimum-score,"from bisect import bisect_right

def minimumScore(s: str, t: str) -> int:
    n, m = len(s), len(t)
    
    # Calculate the leftmost positions where t can be a subsequence of s
    left = [-1] * m
    j = 0
    for i in range(n):
        if j < m and s[i] == t[j]:
            left[j] = i
            j += 1
    
    # Calculate the rightmost positions where t can be a subsequence of s
    right = [-1] * m
    j = m - 1
    for i in range(n - 1, -1, -1):
        if j >= 0 and s[i] == t[j]:
            right[j] = i
            j -= 1
    
    # If t is already a subsequence of s without removing any characters
    if left[-1] != -1:
        return 0
    
    # Find the minimum score by removing characters from t
    min_score = float('inf')
    for i in range(m):
        if left[i] != -1:
            # Use binary search to find the smallest j such that right[j] > left[i]
            j = bisect_right(right, left[i], i, m)
            if j < m:
                min_score = min(min_score, j - i)
    
    # Check for edge cases where minimal removals are required
    for i in range(m):
        if right[i] != -1:
            min_score = min(min_score, i + 1)
    
    return min_score if min_score != float('inf') else m",21,False,16,54,,,,1
242,minimum-number-of-visited-cells-in-a-grid,"
from typing import List
from collections import deque

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    if m == 1 and n == 1:
        return 1
    
    directions = [(0, 1), (1, 0)]
    queue = deque([(0, 0, 1)])  # (row, col, steps)
    visited = set((0, 0))
    
    while queue:
        i, j, steps = queue.popleft()
        
        for di, dj in directions:
            for k in range(1, grid[i][j] + 1):
                ni, nj = i + k * di, j + k * dj
                if 0 <= ni < m and 0 <= nj < n and (ni, nj) not in visited:
                    if ni == m - 1 and nj == n - 1:
                        return steps + 1
                    queue.append((ni, nj, steps + 1))
                    visited.add((ni, nj))
    
    return -1
",21,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
243,minimum-number-of-visited-cells-in-a-grid,"from typing import List
from heapq import heappop, heappush

def minimumVisitedCells(grid: List[List[int]]) -> int:
    m, n = len(grid), len(grid[0])
    if m == 1 and n == 1:
        return -1 if grid[0][0] == 0 else 1
    
    directions = [(0, 1), (1, 0)]
    pq = [(1, 0, 0)]  # (steps, row, col)
    visited = [[False] * n for _ in range(m)]
    visited[0][0] = True
    
    while pq:
        steps, i, j = heappop(pq)
        
        for di, dj in directions:
            for k in range(1, grid[i][j] + 1):
                ni, nj = i + k * di, j + k * dj
                if 0 <= ni < m and 0 <= nj < n and not visited[ni][nj]:
                    if ni == m - 1 and nj == n - 1:
                        return steps + 1
                    heappush(pq, (steps + 1, ni, nj))
                    visited[ni][nj] = True
    
    return -1",21,False,0,0,-1,"**Tests that the code passed:**

assert minimumVisitedCells([[3,4,2,1],[4,2,3,1],[2,1,0,0],[2,4,0,0]]) == 4
assert minimumVisitedCells([[3,4,2,1],[4,2,1,1],[2,1,1,0],[3,4,1,0]]) == 3
assert minimumVisitedCells([[2,1,0],[1,0,0]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
244,rearranging-fruits,"
from typing import List
from collections import Counter

def minCost(basket1: List[int], basket2: List[int]) -> int:
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(basket1 + basket2):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the minimum cost to make both baskets equal
    swaps = []
    for fruit in set(basket1 + basket2):
        diff = abs(count1[fruit] - count2[fruit]) // 2
        if count1[fruit] > count2[fruit]:
            swaps.extend([fruit] * diff)
        elif count2[fruit] > count1[fruit]:
            swaps.extend([fruit] * diff)
    
    swaps.sort()
    min_cost = 0
    for i in range(len(swaps) // 2):
        min_cost += swaps[i]
    
    return min_cost
",21,False,20,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
245,rearranging-fruits,"from typing import List
from collections import Counter
import heapq

def minCost(basket1: List[int], basket2: List[int]) -> int:
    # Check if both baskets are already equal
    if sorted(basket1) == sorted(basket2):
        return 0
    
    count1 = Counter(basket1)
    count2 = Counter(basket2)
    
    # Check if it's possible to make both baskets equal
    for fruit in set(basket1 + basket2):
        if (count1[fruit] + count2[fruit]) % 2 != 0:
            return -1
    
    # Calculate the minimum cost to make both baskets equal
    swaps = []
    for fruit in set(basket1 + basket2):
        diff = abs(count1[fruit] - count2[fruit]) // 2
        if count1[fruit] > count2[fruit]:
            swaps.extend([fruit] * diff)
        elif count2[fruit] > count1[fruit]:
            swaps.extend([fruit] * diff)
    
    # Use a min-heap to optimize the sorting process
    heapq.heapify(swaps)
    min_cost = 0
    for i in range(len(swaps) // 2):
        min_cost += heapq.heappop(swaps)
    
    return min_cost",21,False,20,40,,"**Tests that the code passed:**

assert minCost([4,2,2,2], [1,4,1,2]) == 1
assert minCost([2,3,4,1], [3,2,5,1]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
246,lexicographically-smallest-beautiful-string,"
def smallestBeautifulString(s: str, k: int) -> str:
    def is_beautiful(s: str) -> bool:
        for i in range(len(s) - 1):
            if s[i] == s[i + 1]:
                return False
            if i < len(s) - 2 and s[i] == s[i + 2]:
                return False
        return True

    n = len(s)
    s = list(s)
    for i in range(n - 1, -1, -1):
        for j in range(ord(s[i]) - ord('a') + 1, k):
            s[i] = chr(ord('a') + j)
            if is_beautiful(s[:i + 1]):
                for l in range(i + 1, n):
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s[:l + 1]):
                            break
                return ''.join(s)
        s[i] = 'a'
    return """"
",21,False,1042,1054,,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",0
247,lexicographically-smallest-beautiful-string,"def smallestBeautifulString(s: str, k: int) -> str:
    def is_beautiful(s: str, pos: int) -> bool:
        if pos > 0 and s[pos] == s[pos - 1]:
            return False
        if pos > 1 and s[pos] == s[pos - 2]:
            return False
        return True

    n = len(s)
    s = list(s)
    for i in range(n - 1, -1, -1):
        for j in range(ord(s[i]) - ord('a') + 1, k):
            s[i] = chr(ord('a') + j)
            if is_beautiful(s, i):
                for l in range(i + 1, n):
                    for m in range(k):
                        s[l] = chr(ord('a') + m)
                        if is_beautiful(s, l):
                            break
                return ''.join(s)
        s[i] = 'a'
    return """"",21,True,1054,1054,237 ms,"**Tests that the code passed:**

assert smallestBeautifulString(""abcz"", 26) == ""abda""
assert smallestBeautifulString(""dc"", 4) == """"

**Tests that the code failed:**

No tests failed.
","(True, True)",1
248,count-increasing-quadruplets,"
def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    for i in range(n - 3):
        for j in range(i + 1, n - 2):
            for k in range(j + 1, n - 1):
                for l in range(k + 1, n):
                    if nums[i] < nums[k] < nums[j] < nums[l]:
                        count += 1
                        
    return count
",21,False,63,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2
assert countQuadruplets([1,2,3,4]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
249,count-increasing-quadruplets,"from typing import List

def countQuadruplets(nums: List[int]) -> int:
    n = len(nums)
    count = 0
    
    # Iterate through all possible quadruplets
    for i in range(n - 3):
        for j in range(i + 1, n - 2):
            for k in range(j + 1, n - 1):
                for l in range(k + 1, n):
                    if nums[i] < nums[k] < nums[j] < nums[l]:
                        count += 1
    
    return count",21,False,63,121,,"**Tests that the code passed:**

assert countQuadruplets([1,3,2,4,5]) == 2
assert countQuadruplets([1,2,3,4]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
250,put-marbles-in-bags,"
from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    if k == 1:
        return 0
    
    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the minimum and maximum possible scores
    costs.sort()
    
    # The minimum score is the sum of the smallest k-1 costs
    min_score = sum(costs[:k-1])
    
    # The maximum score is the sum of the largest k-1 costs
    max_score = sum(costs[-(k-1):])
    
    return max_score - min_score
",21,True,103,103,541 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
251,put-marbles-in-bags,"from typing import List

def putMarbles(weights: List[int], k: int) -> int:
    n = len(weights)
    if k == 1:
        return 0
    if k > n:
        raise ValueError(""k cannot be greater than the number of marbles"")
    
    # Calculate the cost of each possible bag
    costs = [weights[i] + weights[i + 1] for i in range(n - 1)]
    
    # Sort the costs to find the k-1 smallest and largest costs
    costs.sort()
    
    min_costs = costs[:k-1]
    max_costs = costs[-(k-1):]
    
    min_score = sum(min_costs)
    max_score = sum(max_costs)
    
    return max_score - min_score",21,True,103,103,533 ms,"**Tests that the code passed:**

assert putMarbles([1,3,5,1], 2) == 4
assert putMarbles([1,3], 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
252,shortest-cycle-in-a-graph,"
from typing import List
from collections import deque, defaultdict

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    def bfs(start):
        dist = [-1] * n
        dist[start] = 0
        queue = deque([(start, -1)])
        while queue:
            node, parent = queue.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    queue.append((neighbor, node))
                elif neighbor != parent:
                    return dist[node] + dist[neighbor] + 1
        return float('inf')
    
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i))
    
    return shortest_cycle if shortest_cycle != float('inf') else -1
",21,False,84,88,,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
253,shortest-cycle-in-a-graph,"from typing import List
from collections import deque, defaultdict

def findShortestCycle(n: int, edges: List[List[int]]) -> int:
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    def bfs(start):
        dist = [-1] * n
        dist[start] = 0
        queue = deque([(start, -1)])
        while queue:
            node, parent = queue.popleft()
            for neighbor in graph[node]:
                if dist[neighbor] == -1:
                    dist[neighbor] = dist[node] + 1
                    queue.append((neighbor, node))
                elif neighbor != parent:
                    cycle_length = dist[node] + dist[neighbor] + 1
                    if cycle_length == 3:
                        return 3  # Early termination for the shortest possible cycle
                    return cycle_length
        return float('inf')
    
    shortest_cycle = float('inf')
    for i in range(n):
        shortest_cycle = min(shortest_cycle, bfs(i))
    
    return shortest_cycle if shortest_cycle != float('inf') else -1",21,False,84,88,,"**Tests that the code passed:**

assert findShortestCycle(7, [[0,1],[1,2],[2,0],[3,4],[4,5],[5,6],[6,3]]) == 3
assert findShortestCycle(4, [[0,1],[0,2]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
254,minimum-time-to-complete-all-tasks,"
from typing import List

def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    time_on = set()
    
    for start, end, duration in tasks:
        current_duration = sum(1 for t in range(start, end + 1) if t in time_on)
        remaining_duration = duration - current_duration
        
        for t in range(end, start - 1, -1):
            if remaining_duration <= 0:
                break
            if t not in time_on:
                time_on.add(t)
                remaining_duration -= 1
    
    return len(time_on)
",21,True,1059,1059,871 ms,"**Tests that the code passed:**

assert findMinimumTime([[2,3,1],[4,5,1],[1,5,2]]) == 2
assert findMinimumTime([[1,3,2],[2,5,3],[5,6,2]]) == 4

**Tests that the code failed:**

No tests failed.
","(True, True)",0
255,minimum-time-to-complete-all-tasks,"from typing import List

def findMinimumTime(tasks: List[List[int]]) -> int:
    tasks.sort(key=lambda x: x[1])
    active_time_slots = set()
    
    for start, end, duration in tasks:
        current_duration = sum(1 for t in range(start, end + 1) if t in active_time_slots)
        remaining_duration = duration - current_duration
        
        for t in range(end, start - 1, -1):
            if remaining_duration <= 0:
                break
            if t not in active_time_slots:
                active_time_slots.add(t)
                remaining_duration -= 1
    
    return len(active_time_slots)",21,True,1059,1059,855 ms,"**Tests that the code passed:**

assert findMinimumTime([[2,3,1],[4,5,1],[1,5,2]]) == 2
assert findMinimumTime([[1,3,2],[2,5,3],[5,6,2]]) == 4

**Tests that the code failed:**

No tests failed.
","(True, True)",1
256,count-number-of-possible-root-nodes,"
from collections import defaultdict, deque
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node)

    def count_correct_guesses(root):
        correct_count = 0
        for u, v in guesses:
            if parent_map[v] == u:
                correct_count += 1
        return correct_count

    n = len(edges) + 1
    tree = build_tree(edges)
    parent_map = [-1] * n

    possible_roots = 0
    for root in range(n):
        parent_map = [-1] * n
        dfs(root, -1)
        if count_correct_guesses(root) >= k:
            possible_roots += 1

    return possible_roots
",21,False,37,51,,"**Tests that the code passed:**

assert rootCount([[0,1],[1,2],[1,3],[4,2]], [[1,3],[0,1],[1,0],[2,4]], 3) == 3
assert rootCount([[0,1],[1,2],[2,3],[3,4]], [[1,0],[3,4],[2,1],[3,2]], 1) == 5

**Tests that the code failed:**

No tests failed.
","(True, True)",0
257,count-number-of-possible-root-nodes,"from collections import defaultdict
from typing import List

def rootCount(edges: List[List[int]], guesses: List[List[int]], k: int) -> int:
    def build_tree(edges):
        tree = defaultdict(list)
        for u, v in edges:
            tree[u].append(v)
            tree[v].append(u)
        return tree

    def dfs(node, parent):
        for neighbor in tree[node]:
            if neighbor != parent:
                parent_map[neighbor] = node
                dfs(neighbor, node)

    def count_correct_guesses():
        correct_count = 0
        for u, v in guesses:
            if parent_map[v] == u:
                correct_count += 1
        return correct_count

    def reroot_and_count(node, parent, correct_count):
        if correct_count >= k:
            possible_roots[0] += 1
        for neighbor in tree[node]:
            if neighbor != parent:
                new_correct_count = correct_count
                if parent_map[neighbor] == node:
                    new_correct_count -= 1
                if parent_map[node] == neighbor:
                    new_correct_count += 1
                reroot_and_count(neighbor, node, new_correct_count)

    n = len(edges) + 1
    tree = build_tree(edges)
    parent_map = [-1] * n

    # Initial DFS to set up parent_map
    dfs(0, -1)
    initial_correct_count = count_correct_guesses()

    possible_roots = [0]
    reroot_and_count(0, -1, initial_correct_count)

    return possible_roots[0]",21,False,35,51,,,,1
258,number-of-ways-to-earn-points,"
from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] means the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # We need to update dp array in reverse to avoid recomputation issues
        for t in range(target, -1, -1):
            for k in range(1, count + 1):
                if t - k * marks >= 0:
                    dp[t] = (dp[t] + dp[t - k * marks]) % MOD
                else:
                    break
    
    return dp[target]
",21,True,63,63,1204 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
259,number-of-ways-to-earn-points,"from typing import List

def waysToReachTarget(target: int, types: List[List[int]]) -> int:
    MOD = 10**9 + 7
    
    # Initialize dp array where dp[i] means the number of ways to get i points
    dp = [0] * (target + 1)
    dp[0] = 1  # There's one way to get 0 points: answer no questions
    
    for count, marks in types:
        # Precompute possible sums for the current question type
        new_dp = dp[:]
        for t in range(target + 1):
            for k in range(1, count + 1):
                if t + k * marks <= target:
                    new_dp[t + k * marks] = (new_dp[t + k * marks] + dp[t]) % MOD
                else:
                    break
        dp = new_dp
    
    return dp[target]",21,True,63,63,1427 ms,"**Tests that the code passed:**

assert waysToReachTarget(6, [[6,1],[3,2],[2,3]]) == 7
assert waysToReachTarget(5, [[50,1],[50,2],[50,5]]) == 4
assert waysToReachTarget(18, [[6,1],[3,2],[2,3]]) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
260,split-the-array-to-make-coprime-products,"
from typing import List
from math import gcd
from functools import reduce

def findValidSplit(nums: List[int]) -> int:
    def product(arr):
        return reduce(lambda x, y: x * y, arr, 1)
    
    n = len(nums)
    for i in range(n - 1):
        left_product = product(nums[:i + 1])
        right_product = product(nums[i + 1:])
        if gcd(left_product, right_product) == 1:
            return i
    return -1
",21,False,29,75,,"**Tests that the code passed:**

assert findValidSplit([4,7,8,15,3,5]) == 2
assert findValidSplit([4,7,15,8,3,5]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
261,split-the-array-to-make-coprime-products,"from typing import List
from math import gcd, log
from functools import reduce

def findValidSplit(nums: List[int]) -> int:
    n = len(nums)
    if n == 1:
        return -1

    total_product = reduce(lambda x, y: x * y, nums, 1)
    left_product = 1

    for i in range(n - 1):
        left_product *= nums[i]
        right_product = total_product // left_product
        if gcd(left_product, right_product) == 1:
            return i
    return -1",21,False,29,75,,"**Tests that the code passed:**

assert findValidSplit([4,7,8,15,3,5]) == 2
assert findValidSplit([4,7,15,8,3,5]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
262,time-to-cross-a-bridge,"
from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = sorted([(i, t[0] + t[2], t[0], t[1], t[2], t[3]) for i, t in enumerate(time)], key=lambda x: (x[1], x[0]))
    left_waiting = [(0, i) for i in range(k)]
    right_waiting = []
    left_crossing = []
    right_crossing = []
    current_time = 0
    boxes_moved = 0

    while boxes_moved < n:
        while left_crossing and left_crossing[0][0] <= current_time:
            _, i = heapq.heappop(left_crossing)
            heapq.heappush(left_waiting, (current_time + workers[i][5], i))
        
        while right_crossing and right_crossing[0][0] <= current_time:
            _, i = heapq.heappop(right_crossing)
            heapq.heappush(right_waiting, (current_time + workers[i][3], i))
        
        if right_waiting:
            _, i = heapq.heappop(right_waiting)
            current_time += workers[i][4]
            heapq.heappush(left_crossing, (current_time, i))
            boxes_moved += 1
        elif left_waiting and boxes_moved < n:
            _, i = heapq.heappop(left_waiting)
            current_time += workers[i][2]
            heapq.heappush(right_crossing, (current_time, i))
        else:
            if left_crossing:
                current_time = left_crossing[0][0]
            elif right_crossing:
                current_time = right_crossing[0][0]

    return current_time
",21,False,2,65,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert findCrossingTime(1, 3, [[1,1,2,1],[1,1,3,1],[1,1,4,1]]) == 6 # ERROR: This unit test fails. Output was 3, but expected value was: 6
assert findCrossingTime(3, 2, [[1,9,1,8],[10,10,10,10]]) == 50 # ERROR: This unit test fails. Output was 24, but expected value was: 50","(False, False)",0
263,time-to-cross-a-bridge,"from typing import List
import heapq

def findCrossingTime(n: int, k: int, time: List[List[int]]) -> int:
    workers = sorted([(i, t[0] + t[2], t[0], t[1], t[2], t[3]) for i, t in enumerate(time)], key=lambda x: (x[1], x[0]))
    left_waiting = [(0, i) for i in range(k)]
    right_waiting = []
    left_crossing = []
    right_crossing = []
    current_time = 0
    boxes_moved = 0

    def update_crossing_queues():
        nonlocal current_time
        while left_crossing and left_crossing[0][0] <= current_time:
            _, i = heapq.heappop(left_crossing)
            heapq.heappush(left_waiting, (current_time, i))
        
        while right_crossing and right_crossing[0][0] <= current_time:
            _, i = heapq.heappop(right_crossing)
            heapq.heappush(right_waiting, (current_time, i))

    def get_next_event_time():
        if left_crossing and right_crossing:
            return min(left_crossing[0][0], right_crossing[0][0])
        elif left_crossing:
            return left_crossing[0][0]
        elif right_crossing:
            return right_crossing[0][0]
        return float('inf')

    while boxes_moved < n:
        update_crossing_queues()
        
        if right_waiting:
            _, i = heapq.heappop(right_waiting)
            current_time += workers[i][4]
            heapq.heappush(left_crossing, (current_time + workers[i][3], i))
            boxes_moved += 1
        elif left_waiting and boxes_moved < n:
            _, i = heapq.heappop(left_waiting)
            current_time += workers[i][2]
            heapq.heappush(right_crossing, (current_time + workers[i][1], i))
        else:
            current_time = get_next_event_time()

    return current_time",21,False,1,65,,,,1
264,check-if-point-is-reachable,"
def isReachable(targetX: int, targetY: int) -> bool:
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

    # Reduce targetX and targetY by their greatest common divisor
    g = gcd(targetX, targetY)
    targetX //= g
    targetY //= g

    # Check if both targetX and targetY are powers of 2
    def is_power_of_two(x):
        return (x & (x - 1)) == 0

    return is_power_of_two(targetX) and is_power_of_two(targetY)
",21,False,10,60,,"**Tests that the code passed:**

assert isReachable(6, 9) == False

**Tests that the code failed:**

assert isReachable(4, 7) == True # ERROR: This unit test fails. Output was False, but expected value was: True","(True, False)",0
265,check-if-point-is-reachable,"from math import gcd

def isReachable(targetX: int, targetY: int) -> bool:
    while targetX > 1 and targetY > 1:
        if targetX > targetY:
            targetX %= targetY
        else:
            targetY %= targetX
    return targetX == 1 or targetY == 1",21,False,-1,-1,-1,"**Tests that the code passed:**

assert isReachable(6, 9) == False
assert isReachable(4, 7) == True

**Tests that the code failed:**

No tests failed.
","(True, True)",1
266,minimum-cost-to-split-an-array,"
from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    def importance_value(subarray: List[int]) -> int:
        count = defaultdict(int)
        for num in subarray:
            count[num] += 1
        trimmed_length = sum(v for v in count.values() if v > 1)
        return k + trimmed_length

    n = len(nums)
    dp = [float('inf')] * (n + 1)
    dp[0] = 0

    for i in range(1, n + 1):
        for j in range(i):
            dp[i] = min(dp[i], dp[j] + importance_value(nums[j:i]))

    return dp[n]
",21,False,34,59,,"**Tests that the code passed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8
assert minCost([1,2,1,2,1], 2) == 6
assert minCost([1,2,1,2,1], 5) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
267,minimum-cost-to-split-an-array,"from typing import List
from collections import defaultdict

def minCost(nums: List[int], k: int) -> int:
    def importance_value(subarray: List[int]) -> int:
        count = defaultdict(int)
        for num in subarray:
            count[num] += 1
        trimmed_length = sum(v for v in count.values() if v > 1)
        return k + trimmed_length

    n = len(nums)
    dp = [float('inf')] * (n + 1)
    dp[0] = 0

    # Sliding window approach to maintain frequency counts
    for i in range(1, n + 1):
        count = defaultdict(int)
        trimmed_length = 0
        for j in range(i, 0, -1):
            count[nums[j-1]] += 1
            if count[nums[j-1]] == 2:
                trimmed_length += 2
            elif count[nums[j-1]] > 2:
                trimmed_length += 1
            dp[i] = min(dp[i], dp[j-1] + k + trimmed_length)

    return dp[n]",21,True,59,59,3259 ms,"**Tests that the code passed:**

assert minCost([1,2,1,2,1,3,3], 2) == 8
assert minCost([1,2,1,2,1], 2) == 6
assert minCost([1,2,1,2,1], 5) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
268,difference-between-maximum-and-minimum-price-sum,"
from typing import List
from collections import defaultdict

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    def dfs(node, parent):
        max1, max2 = 0, 0
        for neighbor in graph[node]:
            if neighbor == parent:
                continue
            sub_max = dfs(neighbor, node)
            if sub_max > max1:
                max1, max2 = sub_max, max1
            elif sub_max > max2:
                max2 = sub_max
        max_path[node] = max1 + price[node]
        return max_path[node]

    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)

    max_path = [0] * n
    dfs(0, -1)

    max_cost = 0
    for node in range(n):
        max_cost = max(max_cost, max_path[node] - price[node])

    return max_cost
",21,False,12,58,,"**Tests that the code passed:**

assert maxOutput(6, [[0,1],[1,2],[1,3],[3,4],[3,5]], [9,8,7,6,10,5]) == 24
assert maxOutput(3, [[0,1],[1,2]], [1,1,1]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
269,difference-between-maximum-and-minimum-price-sum,"from typing import List
from collections import defaultdict

def maxOutput(n: int, edges: List[List[int]], price: List[int]) -> int:
    def dfs(node, parent):
        max1, max2 = 0, 0
        for neighbor in graph[node]:
            if neighbor == parent:
                continue
            sub_max = dfs(neighbor, node)
            if sub_max > max1:
                max1, max2 = sub_max, max1
            elif sub_max > max2:
                max2 = sub_max
        max_path[node] = max1 + price[node]
        min_path[node] = min(max1, price[node])
        return max_path[node]

    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)

    max_cost = 0
    for root in range(n):
        max_path = [0] * n
        min_path = [float('inf')] * n
        dfs(root, -1)
        for node in range(n):
            max_cost = max(max_cost, max_path[node] - min_path[node])

    return max_cost",21,False,37,58,,"**Tests that the code passed:**

assert maxOutput(6, [[0,1],[1,2],[1,3],[3,4],[3,5]], [9,8,7,6,10,5]) == 24
assert maxOutput(3, [[0,1],[1,2]], [1,1,1]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",1
270,maximize-the-minimum-powered-city,"
from typing import List

def maxPower(stations: List[int], r: int, k: int) -> int:
    def is_possible(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        
        for i in range(len(stations)):
            if i > r:
                current_power -= additional_stations[i - r - 1]
            
            if stations[i] + current_power < min_power:
                needed = min_power - (stations[i] + current_power)
                if total_additional + needed > k:
                    return False
                additional_stations[min(i + r, len(stations) - 1)] += needed
                current_power += needed
                total_additional += needed
        
        return True
    
    low, high = 0, sum(stations) + k
    while low < high:
        mid = (low + high + 1) // 2
        if is_possible(mid):
            low = mid
        else:
            high = mid - 1
    
    return low
",21,False,9,30,,"**Tests that the code passed:**

assert maxPower([4,4,4,4], 0, 3) == 4

**Tests that the code failed:**

assert maxPower([1,2,4,5,0], 1, 2) == 5 # ERROR: This unit test fails. Output was 1, but expected value was: 5","(False, True)",0
271,maximize-the-minimum-powered-city,"from typing import List
from collections import deque

def maxPower(stations: List[int], r: int, k: int) -> int:
    def is_possible(min_power: int) -> bool:
        additional_stations = [0] * len(stations)
        current_power = 0
        total_additional = 0
        window_sum = 0
        window = deque()
        
        for i in range(len(stations)):
            if i > r:
                window_sum -= additional_stations[i - r - 1]
            
            current_power = stations[i] + window_sum
            
            if current_power < min_power:
                needed = min_power - current_power
                if total_additional + needed > k:
                    return False
                additional_stations[min(i + r, len(stations) - 1)] += needed
                window_sum += needed
                total_additional += needed
        
        return True
    
    low, high = 0, max(stations) + k
    while low < high:
        mid = (low + high + 1) // 2
        if is_possible(mid):
            low = mid
        else:
            high = mid - 1
    
    return low",21,False,9,30,,,,1
272,count-anagrams,"
from collections import Counter
from math import factorial

MOD = 10**9 + 7

def countAnagrams(s: str) -> int:
    def count_word_anagrams(word):
        freq = Counter(word)
        word_len = len(word)
        denom = 1
        for count in freq.values():
            denom = (denom * factorial(count)) % MOD
        return (factorial(word_len) * pow(denom, MOD-2, MOD)) % MOD

    words = s.split()
    result = 1
    for word in words:
        result = (result * count_word_anagrams(word)) % MOD

    return result

# Example usage:
# print(countAnagrams(""too hot""))  # Output: 18
# print(countAnagrams(""aa""))       # Output: 1
",21,False,0,41,,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",0
273,count-anagrams,"from collections import Counter
from math import factorial

MOD = 10**9 + 7

# Precompute factorials and their modular inverses up to the maximum possible word length
MAX_WORD_LENGTH = 10**5
factorials = [1] * (MAX_WORD_LENGTH + 1)
inv_factorials = [1] * (MAX_WORD_LENGTH + 1)

for i in range(2, MAX_WORD_LENGTH + 1):
    factorials[i] = factorials[i-1] * i % MOD

inv_factorials[MAX_WORD_LENGTH] = pow(factorials[MAX_WORD_LENGTH], MOD-2, MOD)
for i in range(MAX_WORD_LENGTH - 1, 0, -1):
    inv_factorials[i] = inv_factorials[i + 1] * (i + 1) % MOD

def countAnagrams(s: str) -> int:
    def count_word_anagrams(word):
        freq = Counter(word)
        word_len = len(word)
        denom = 1
        for count in freq.values():
            denom = (denom * inv_factorials[count]) % MOD
        return (factorials[word_len] * denom) % MOD

    if not s:
        return 0

    words = s.split()
    result = 1
    for word in words:
        result = (result * count_word_anagrams(word)) % MOD

    return result

# Example usage:
# print(countAnagrams(""too hot""))  # Output: 18
# print(countAnagrams(""aa""))       # Output: 1",21,False,0,41,,"**Tests that the code passed:**

assert countAnagrams(""too hot"") == 18
assert countAnagrams(""aa"") == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
274,number-of-great-partitions,"
from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    dp = [0] * (total_sum + 1)
    dp[0] = 1
    
    for num in nums:
        for j in range(total_sum, num - 1, -1):
            dp[j] = (dp[j] + dp[j - num]) % MOD
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp[i]) % MOD
    
    return result

# Example usage:
# print(countPartitions([1, 2, 3, 4], 4))  # Output: 6
# print(countPartitions([3, 3, 3], 4))     # Output: 0
# print(countPartitions([6, 6], 2))        # Output: 2
",21,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
275,number-of-great-partitions,"from typing import List

def countPartitions(nums: List[int], k: int) -> int:
    MOD = 10**9 + 7
    total_sum = sum(nums)
    
    if total_sum < 2 * k:
        return 0
    
    # Use a 2D DP array to track partitions
    dp = [[0] * (total_sum + 1) for _ in range(len(nums) + 1)]
    dp[0][0] = 1
    
    for i in range(1, len(nums) + 1):
        for j in range(total_sum + 1):
            dp[i][j] = dp[i - 1][j]
            if j >= nums[i - 1]:
                dp[i][j] = (dp[i][j] + dp[i - 1][j - nums[i - 1]]) % MOD
    
    result = 0
    for i in range(k, total_sum - k + 1):
        result = (result + dp[len(nums)][i]) % MOD
    
    return result

# Example usage:
# print(countPartitions([1, 2, 3, 4], 4))  # Output: 6
# print(countPartitions([3, 3, 3], 4))     # Output: 0
# print(countPartitions([6, 6], 2))        # Output: 2",21,False,32,45,,"**Tests that the code passed:**

assert countPartitions([1,2,3,4], 4) == 6
assert countPartitions([3,3,3], 4) == 0
assert countPartitions([6,6], 2) == 2

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
276,cycle-length-queries-in-a-tree,"
from typing import List

def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node):
        path = []
        while node > 0:
            path.append(node)
            node //= 2
        return path

    def find_cycle_length(a, b):
        path_a = find_path_to_root(a)
        path_b = find_path_to_root(b)
        set_b = set(path_b)
        
        for i, node in enumerate(path_a):
            if node in set_b:
                j = path_b.index(node)
                return i + j + 1
        return 0

    answer = []
    for a, b in queries:
        cycle_length = find_cycle_length(a, b)
        answer.append(cycle_length)
    
    return answer
",21,True,27,27,1207 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
277,cycle-length-queries-in-a-tree,"from typing import List

def cycleLengthQueries(n: int, queries: List[List[int]]) -> List[int]:
    def find_path_to_root(node):
        path = []
        while node > 0:
            path.append(node)
            node //= 2
        return path

    def find_cycle_length(a, b):
        path_a = find_path_to_root(a)
        path_b = find_path_to_root(b)
        set_b = set(path_b)
        
        for i, node in enumerate(path_a):
            if node in set_b:
                j = path_b.index(node)
                return i + j + 1
        return 0

    answer = []
    for a, b in queries:
        cycle_length = find_cycle_length(a, b)
        answer.append(cycle_length)
    
    return answer",21,True,27,27,1245 ms,"**Tests that the code passed:**

assert cycleLengthQueries(3, [[5,3],[4,7],[2,3]]) == [4,5,3]
assert cycleLengthQueries(2, [[1,2]]) == [2]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
278,add-edges-to-make-degrees-of-all-nodes-even,"
from typing import List
from collections import defaultdict

def isPossible(n: int, edges: List[List[int]]) -> bool:
    degree = [0] * (n + 1)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    odd_degree_nodes = [i for i in range(1, n + 1) if degree[i] % 2 == 1]

    if len(odd_degree_nodes) == 0:
        return True
    if len(odd_degree_nodes) == 2:
        return True
    if len(odd_degree_nodes) == 4:
        u, v, w, x = odd_degree_nodes
        existing_edges = set((min(a, b), max(a, b)) for a, b in edges)
        if (min(u, v), max(u, v)) not in existing_edges and (min(w, x), max(w, x)) not in existing_edges:
            return True
        if (min(u, w), max(u, w)) not in existing_edges and (min(v, x), max(v, x)) not in existing_edges:
            return True
        if (min(u, x), max(u, x)) not in existing_edges and (min(v, w), max(v, w)) not in existing_edges:
            return True
    return False
",21,False,47,52,,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True
assert isPossible(4, [[1,2],[1,3],[1,4]]) == False

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
279,add-edges-to-make-degrees-of-all-nodes-even,"from typing import List
from collections import defaultdict, deque

def isPossible(n: int, edges: List[List[int]]) -> bool:
    def bfs(start, visited, graph):
        queue = deque([start])
        visited[start] = True
        while queue:
            node = queue.popleft()
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    visited[neighbor] = True
                    queue.append(neighbor)

    def all_nodes_connected(n, edges):
        graph = defaultdict(list)
        for u, v in edges:
            graph[u].append(v)
            graph[v].append(u)
        visited = [False] * (n + 1)
        for i in range(1, n + 1):
            if graph[i]:
                bfs(i, visited, graph)
                break
        return all(visited[i] or not graph[i] for i in range(1, n + 1))

    degree = [0] * (n + 1)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1

    odd_degree_nodes = [i for i in range(1, n + 1) if degree[i] % 2 == 1]

    if len(odd_degree_nodes) == 0:
        return True
    if len(odd_degree_nodes) == 2:
        return True
    if len(odd_degree_nodes) == 4:
        u, v, w, x = odd_degree_nodes
        existing_edges = set((min(a, b), max(a, b)) for a, b in edges)
        if (min(u, v), max(u, v)) not in existing_edges and (min(w, x), max(w, x)) not in existing_edges:
            return True
        if (min(u, w), max(u, w)) not in existing_edges and (min(v, x), max(v, x)) not in existing_edges:
            return True
        if (min(u, x), max(u, x)) not in existing_edges and (min(v, w), max(v, w)) not in existing_edges:
            return True

    return False",21,False,47,52,,"**Tests that the code passed:**

assert isPossible(5, [[1,2],[2,3],[3,4],[4,2],[1,4],[2,5]]) == True
assert isPossible(4, [[1,2],[3,4]]) == True
assert isPossible(4, [[1,2],[1,3],[1,4]]) == False

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
280,minimum-total-cost-to-make-arrays-unequal,"
from typing import List

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    swaps = []
    
    for i in range(n):
        if nums1[i] == nums2[i]:
            swaps.append(i)
    
    if not swaps:
        return 0
    
    swaps.sort()
    total_cost = 0
    
    for i in range(len(swaps)):
        if i == 0:
            total_cost += swaps[i]
        else:
            total_cost += swaps[i] + swaps[i-1]
    
    return total_cost if total_cost > 0 else -1
",21,False,7,104,,"**Tests that the code passed:**

assert minimumTotalCost([2,2,2,1,3], [1,2,2,3,3]) == 10

**Tests that the code failed:**

assert minimumTotalCost([1,2,3,4,5], [1,2,3,4,5]) == 10 # ERROR: This unit test fails. Output was 16, but expected value was: 10
assert minimumTotalCost([1,2,2], [1,2,2]) == -1 # ERROR: This unit test fails. Output was 4, but expected value was: -1","(False, True, False)",0
281,minimum-total-cost-to-make-arrays-unequal,"from typing import List
import heapq

def minimumTotalCost(nums1: List[int], nums2: List[int]) -> int:
    n = len(nums1)
    if n == 0:
        return -1
    
    # Check if it's already valid
    if all(nums1[i] != nums2[i] for i in range(n)):
        return 0
    
    # Priority queue to store the minimum cost swaps
    min_heap = []
    for i in range(n):
        for j in range(i + 1, n):
            if nums1[i] != nums2[j] and nums1[j] != nums2[i]:
                heapq.heappush(min_heap, (i + j, i, j))
    
    total_cost = 0
    swapped = set()
    
    def is_valid():
        return all(nums1[k] != nums2[k] for k in range(n))
    
    while min_heap:
        cost, i, j = heapq.heappop(min_heap)
        if i in swapped or j in swapped:
            continue
        nums1[i], nums1[j] = nums1[j], nums1[i]
        total_cost += cost
        swapped.add(i)
        swapped.add(j)
        if is_valid():
            return total_cost
    
    # Re-evaluate swaps to ensure all possibilities are considered
    for i in range(n):
        for j in range(i + 1, n):
            if nums1[i] != nums2[j] and nums1[j] != nums2[i] and (i not in swapped or j not in swapped):
                nums1[i], nums1[j] = nums1[j], nums1[i]
                total_cost += i + j
                swapped.add(i)
                swapped.add(j)
                if is_valid():
                    return total_cost
                nums1[i], nums1[j] = nums1[j], nums1[i]  # revert swap if not valid
    
    return -1",21,False,56,104,,"**Tests that the code passed:**

assert minimumTotalCost([1,2,3,4,5], [1,2,3,4,5]) == 10
assert minimumTotalCost([2,2,2,1,3], [1,2,2,3,3]) == 10
assert minimumTotalCost([1,2,2], [1,2,2]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
282,maximum-number-of-points-from-grid-queries,"
from typing import List
from collections import deque

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    def bfs(query):
        m, n = len(grid), len(grid[0])
        visited = [[False] * n for _ in range(m)]
        queue = deque([(0, 0)])
        points = 0
        
        while queue:
            x, y = queue.popleft()
            if visited[x][y]:
                continue
            visited[x][y] = True
            
            if grid[x][y] >= query:
                continue
            
            points += 1
            
            for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                nx, ny = x + dx, y + dy
                if 0 <= nx < m and 0 <= ny < n and not visited[nx][ny]:
                    queue.append((nx, ny))
        
        return points
    
    return [bfs(query) for query in queries]
",21,False,17,21,,"**Tests that the code passed:**

assert maxPoints([[1,2,3],[2,5,7],[3,5,1]], [5,6,2]) == [5,8,1]
assert maxPoints([[5,2,1],[1,1,2]], [3]) == [0]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
283,maximum-number-of-points-from-grid-queries,"from typing import List
import heapq

def maxPoints(grid: List[List[int]], queries: List[int]) -> List[int]:
    m, n = len(grid), len(grid[0])
    max_points = [0] * len(queries)
    query_indices = sorted(range(len(queries)), key=lambda i: queries[i])
    sorted_queries = sorted(queries)
    
    for idx in query_indices:
        query = sorted_queries[idx]
        visited = [[False] * n for _ in range(m)]
        min_heap = [(grid[0][0], 0, 0)]
        points = 0
        
        while min_heap:
            value, x, y = heapq.heappop(min_heap)
            if value >= query:
                continue
            if not visited[x][y]:
                points += 1
                visited[x][y] = True
                
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < m and 0 <= ny < n and not visited[nx][ny]:
                        heapq.heappush(min_heap, (grid[nx][ny], nx, ny))
        
        max_points[idx] = points
    
    return [max_points[query_indices.index(i)] for i in range(len(queries))]",21,False,17,21,,,,1
284,divide-nodes-into-the-maximum-number-of-groups,"
from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1
        
        return max_depth + 1
    
    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    visited_global = set()
    max_groups = 0
    
    for node in range(1, n + 1):
        if node not in visited_global:
            result = bfs(node)
            if result == -1:
                return -1
            max_groups += result
            visited_global.update(graph[node])
    
    return max_groups
",21,False,11,55,,"**Tests that the code passed:**

assert magnificentSets(3, [[1,2],[2,3],[3,1]]) == -1

**Tests that the code failed:**

assert magnificentSets(6, [[1,2],[1,4],[1,5],[2,6],[2,3],[4,6]]) == 4 # ERROR: This unit test fails. Output was 11, but expected value was: 4","(False, True)",0
285,divide-nodes-into-the-maximum-number-of-groups,"from typing import List
from collections import deque, defaultdict

def magnificentSets(n: int, edges: List[List[int]]) -> int:
    def bfs(node):
        queue = deque([(node, 0)])
        visited = {node: 0}
        max_depth = 0
        
        while queue:
            current, depth = queue.popleft()
            max_depth = max(max_depth, depth)
            
            for neighbor in graph[current]:
                if neighbor not in visited:
                    visited[neighbor] = depth + 1
                    queue.append((neighbor, depth + 1))
                elif abs(visited[neighbor] - depth) != 1:
                    return -1, set()
        
        return max_depth + 1, set(visited.keys())
    
    def explore_component(node):
        queue = deque([node])
        component = set()
        while queue:
            current = queue.popleft()
            if current not in component:
                component.add(current)
                for neighbor in graph[current]:
                    if neighbor not in component:
                        queue.append(neighbor)
        return component
    
    graph = defaultdict(list)
    for a, b in edges:
        graph[a].append(b)
        graph[b].append(a)
    
    visited_global = set()
    max_groups = 0
    
    for node in range(1, n + 1):
        if node not in visited_global:
            component = explore_component(node)
            local_max_groups = 0
            for start_node in component:
                result, visited_nodes = bfs(start_node)
                if result == -1:
                    return -1
                local_max_groups = max(local_max_groups, result)
            max_groups += local_max_groups
            visited_global.update(component)
    
    return max_groups if edges else n",21,True,55,55,2666 ms,"**Tests that the code passed:**

assert magnificentSets(6, [[1,2],[1,4],[1,5],[2,6],[2,3],[4,6]]) == 4
assert magnificentSets(3, [[1,2],[2,3],[3,1]]) == -1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
286,count-palindromic-subsequences,"
def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Initialize for subsequences of length 1
    for i in range(n):
        dp[i][i][1] = 1
    
    # Fill the dp array
    for length in range(2, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            for k in range(1, 6):
                if s[i] == s[j]:
                    if k == 1:
                        dp[i][j][k] = 2
                    else:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
                else:
                    dp[i][j][k] = (dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
    
    # Sum up all palindromic subsequences of length 5
    result = 0
    for i in range(n):
        for j in range(i, n):
            result = (result + dp[i][j][5]) % MOD
    
    return result
",21,False,0,63,,"**Tests that the code passed:**

assert countPalindromes(""103301"") == 2

**Tests that the code failed:**

assert countPalindromes(""0000000"") == 21 # ERROR: This unit test fails. Output was 35, but expected value was: 21
assert countPalindromes(""9999900000"") == 2 # ERROR: This unit test fails. Output was 12, but expected value was: 2","(True, False, False)",0
287,count-palindromic-subsequences,"from collections import defaultdict

def countPalindromes(s: str) -> int:
    MOD = 10**9 + 7
    n = len(s)
    
    if n < 5:
        return 0
    
    # dp[i][j][k] will store the number of palindromic subsequences of length k in s[i:j+1]
    dp = [[[0] * 6 for _ in range(n)] for _ in range(n)]
    
    # Initialize for subsequences of length 1
    for i in range(n):
        dp[i][i][1] = 1
    
    # Initialize for subsequences of length 2
    for i in range(n - 1):
        dp[i][i + 1][1] = 2
        if s[i] == s[i + 1]:
            dp[i][i + 1][2] = 1
    
    # Fill the dp array
    for length in range(3, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            for k in range(1, 6):
                if s[i] == s[j]:
                    if k == 2:
                        dp[i][j][k] = 1
                    elif k > 2:
                        dp[i][j][k] = (dp[i + 1][j - 1][k - 2] + dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
                else:
                    dp[i][j][k] = (dp[i + 1][j][k] + dp[i][j - 1][k] - dp[i + 1][j - 1][k]) % MOD
    
    # Sum up all palindromic subsequences of length 5
    result = 0
    for i in range(n):
        for j in range(i, n):
            result = (result + dp[i][j][5]) % MOD
    
    return result",21,False,0,63,,,,1
288,count-subarrays-with-median-k,"
def countSubarrays(nums: List[int], k: int) -> int:
    from collections import defaultdict

    def count_medians(arr, k):
        balance = 0
        count = 0
        prefix_counts = defaultdict(int)
        prefix_counts[0] = 1

        for num in arr:
            if num < k:
                balance -= 1
            elif num > k:
                balance += 1
            else:
                count += prefix_counts[balance] + prefix_counts[balance - 1]
                balance += 1

            prefix_counts[balance] += 1

        return count

    return count_medians(nums, k)
",21,False,12,45,,"**Tests that the code passed:**

assert countSubarrays([2,3,1], 3) == 1

**Tests that the code failed:**

assert countSubarrays([3,2,1,4,5], 4) == 3 # ERROR: This unit test fails. Output was 1, but expected value was: 3","(False, True)",0
289,count-subarrays-with-median-k,"def countSubarrays(nums: List[int], k: int) -> int:
    from collections import defaultdict

    def count_medians(arr, k):
        balance = 0
        count = 0
        prefix_counts = defaultdict(int)
        prefix_counts[0] = 1
        found_k = False

        for num in arr:
            if num < k:
                balance -= 1
            elif num > k:
                balance += 1
            else:
                found_k = True

            if found_k:
                count += prefix_counts[balance] + prefix_counts[balance - 1]
            else:
                prefix_counts[balance] += 1

        return count

    return count_medians(nums, k)",21,True,45,45,350 ms,"**Tests that the code passed:**

assert countSubarrays([3,2,1,4,5], 4) == 3
assert countSubarrays([2,3,1], 3) == 1

**Tests that the code failed:**

No tests failed.
","(True, True)",1
290,number-of-beautiful-partitions,"
def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    
    def is_beautiful(start, end):
        return s[start] in prime_digits and s[end] not in prime_digits
    
    n = len(s)
    dp = [[0] * (k + 1) for _ in range(n + 1)]
    dp[0][0] = 1
    
    for i in range(1, n + 1):
        for j in range(1, k + 1):
            for l in range(minLength, i + 1):
                if is_beautiful(i - l, i - 1):
                    dp[i][j] = (dp[i][j] + dp[i - l][j - 1]) % MOD
    
    return dp[n][k]
",21,False,36,74,,"**Tests that the code passed:**

assert beautifulPartitions(""23542185131"", 3, 2) == 3
assert beautifulPartitions(""23542185131"", 3, 3) == 1
assert beautifulPartitions(""3312958"", 3, 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
291,number-of-beautiful-partitions,"from functools import lru_cache

def beautifulPartitions(s: str, k: int, minLength: int) -> int:
    MOD = 10**9 + 7
    prime_digits = {'2', '3', '5', '7'}
    
    def is_beautiful(start, end):
        return s[start] in prime_digits and s[end] not in prime_digits
    
    n = len(s)
    
    # Edge case handling
    if k > n or minLength * k > n:
        return 0
    if all(c in prime_digits for c in s) or all(c not in prime_digits for c in s):
        return 0
    
    @lru_cache(None)
    def dp(i, j):
        if j == 0:
            return 1 if i == 0 else 0
        if i < j * minLength:
            return 0
        count = 0
        for l in range(minLength, i + 1):
            if is_beautiful(i - l, i - 1):
                count = (count + dp(i - l, j - 1)) % MOD
        return count
    
    return dp(n, k)",21,False,58,74,,"**Tests that the code passed:**

assert beautifulPartitions(""23542185131"", 3, 2) == 3
assert beautifulPartitions(""23542185131"", 3, 3) == 1
assert beautifulPartitions(""3312958"", 3, 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
292,split-message-based-on-limit,"
from typing import List

def splitMessage(message: str, limit: int) -> List[str]:
    def calculate_suffix_length(parts: int) -> int:
        return len(f""<{parts}/{parts}>"")

    def can_split_into_parts(parts: int) -> bool:
        suffix_length = calculate_suffix_length(parts)
        total_length = len(message) + parts * suffix_length
        return total_length <= parts * limit

    n = len(message)
    for parts in range(1, n + 1):
        if can_split_into_parts(parts):
            result = []
            suffix_length = calculate_suffix_length(parts)
            max_message_length = limit - suffix_length
            index = 0
            for part in range(1, parts + 1):
                suffix = f""<{part}/{parts}>""
                part_length = limit - len(suffix)
                result.append(message[index:index + part_length] + suffix)
                index += part_length
            return result
    return []
",21,False,71,94,,"**Tests that the code passed:**

assert splitMessage(""short message"", 15) == [""short mess<1/2>"",""age<2/2>""]

**Tests that the code failed:**

assert splitMessage(""this is really a very awesome message"", 9) == [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""] # ERROR: This unit test fails. Output was ['thi<1/19>', 's i<2/19>', 's r<3/19>', 'eal<4/19>', 'ly <5/19>', 'a v<6/19>', 'ery<7/19>', ' aw<8/19>', 'eso<9/19>', 'me<10/19>', ' m<11/19>', 'es<12/19>', 'sa<13/19>', 'ge<14/19>', '<15/19>', '<16/19>', '<17/19>', '<18/19>', '<19/19>'], but expected value was: [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""]","(False, True)",0
293,split-message-based-on-limit,"from typing import List

def splitMessage(message: str, limit: int) -> List[str]:
    def calculate_suffix_length(parts: int) -> int:
        return len(f""<{parts}/{parts}>"")

    def can_split_into_parts(parts: int) -> bool:
        total_length = len(message)
        for part in range(1, parts + 1):
            suffix_length = len(f""<{part}/{parts}>"")
            total_length += suffix_length
        return total_length <= parts * limit

    n = len(message)
    for parts in range(1, n + 1):
        if can_split_into_parts(parts):
            result = []
            index = 0
            for part in range(1, parts + 1):
                suffix = f""<{part}/{parts}>""
                part_length = limit - len(suffix)
                result.append(message[index:index + part_length] + suffix)
                index += part_length
            if ''.join([part[:-len(f""<{i+1}/{parts}>"")] for i, part in enumerate(result)]) == message:
                return result
    return []",21,False,54,94,,"**Tests that the code passed:**

assert splitMessage(""this is really a very awesome message"", 9) == [""thi<1/14>"",""s i<2/14>"",""s r<3/14>"",""eal<4/14>"",""ly <5/14>"",""a v<6/14>"",""ery<7/14>"","" aw<8/14>"",""eso<9/14>"",""me<10/14>"","" m<11/14>"",""es<12/14>"",""sa<13/14>"",""ge<14/14>""]
assert splitMessage(""short message"", 15) == [""short mess<1/2>"",""age<2/2>""]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
294,maximum-number-of-non-overlapping-palindrome-substrings,"
def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    dp = [0] * (n + 1)

    for i in range(n - k + 1):
        for j in range(i + k, n + 1):
            if is_palindrome(s[i:j]):
                dp[j] = max(dp[j], dp[i] + 1)

    return max(dp)
",21,False,12,56,,"**Tests that the code passed:**

assert maxPalindromes(""adbcda"", 2) == 0

**Tests that the code failed:**

assert maxPalindromes(""abaccdbbd"", 3) == 2 # ERROR: This unit test fails. Output was 1, but expected value was: 2","(False, True)",0
295,maximum-number-of-non-overlapping-palindrome-substrings,"def maxPalindromes(s: str, k: int) -> int:
    def is_palindrome(sub: str) -> bool:
        return sub == sub[::-1]

    n = len(s)
    if k > n:
        return 0

    # Precompute palindrome status for all substrings
    is_pal = [[False] * n for _ in range(n)]
    for i in range(n):
        is_pal[i][i] = True
    for i in range(n - 1):
        is_pal[i][i + 1] = (s[i] == s[i + 1])
    for length in range(3, n + 1):
        for i in range(n - length + 1):
            j = i + length - 1
            is_pal[i][j] = (s[i] == s[j]) and is_pal[i + 1][j - 1]

    # dp[i] will store the maximum number of non-overlapping palindromic substrings ending at index i
    dp = [0] * n
    for i in range(n):
        if i >= k - 1 and is_pal[i - k + 1][i]:
            dp[i] = 1
        if i > 0:
            dp[i] = max(dp[i], dp[i - 1])
        for j in range(i - k + 1):
            if is_pal[j][i]:
                dp[i] = max(dp[i], (dp[j - 1] if j > 0 else 0) + 1)

    return dp[-1]",21,False,0,56,,"**Tests that the code passed:**

assert maxPalindromes(""abaccdbbd"", 3) == 2
assert maxPalindromes(""adbcda"", 2) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
296,minimum-total-distance-traveled,"
from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort()
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for i in range(1, n + 1):
        for j in range(1, m + 1):
            dp[i][j] = dp[i][j - 1]
            total_distance = 0
            for k in range(1, min(factory[j - 1][1], i) + 1):
                total_distance += abs(robot[i - k] - factory[j - 1][0])
                dp[i][j] = min(dp[i][j], dp[i - k][j - 1] + total_distance)
    
    return dp[n][m]
",21,False,0,40,,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",0
297,minimum-total-distance-traveled,"from typing import List

def minimumTotalDistance(robot: List[int], factory: List[List[int]]) -> int:
    robot.sort()
    factory.sort(key=lambda x: (x[0], -x[1]))  # Sort by position, then by limit in descending order
    
    n = len(robot)
    m = len(factory)
    
    dp = [[float('inf')] * (m + 1) for _ in range(n + 1)]
    dp[0][0] = 0
    
    for i in range(1, n + 1):
        for j in range(1, m + 1):
            dp[i][j] = dp[i][j - 1]
            total_distance = 0
            for k in range(1, min(factory[j - 1][1], i) + 1):
                total_distance += abs(robot[i - k] - factory[j - 1][0])
                dp[i][j] = min(dp[i][j], dp[i - k][j - 1] + total_distance)
    
    return dp[n][m]",21,False,0,40,,"**Tests that the code passed:**

assert minimumTotalDistance([0,4,6], [[2,2],[6,2]]) == 4
assert minimumTotalDistance([1,-1], [[-2,1],[2,1]]) == 2

**Tests that the code failed:**

No tests failed.
","(True, True)",1
298,next-greater-element-iv,"
def secondGreaterElement(nums: List[int]) -> List[int]:
    from collections import deque

    n = len(nums)
    answer = [-1] * n
    first_greater = deque()
    second_greater = deque()

    for i in range(n):
        while second_greater and nums[second_greater[-1]] < nums[i]:
            idx = second_greater.pop()
            answer[idx] = nums[i]
        
        temp = deque()
        while first_greater and nums[first_greater[-1]] < nums[i]:
            idx = first_greater.pop()
            temp.appendleft(idx)
        
        while temp:
            second_greater.append(temp.popleft())
        
        first_greater.append(i)
    
    return answer
",21,True,51,51,731 ms,"**Tests that the code passed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1]
assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

No tests failed.
","(True, True)",0
299,next-greater-element-iv,"def secondGreaterElement(nums: List[int]) -> List[int]:
    from collections import deque

    n = len(nums)
    if n == 1:
        return [-1]
    
    answer = [-1] * n
    first_greater = deque()
    second_greater = deque()

    for i in range(n):
        while second_greater and nums[second_greater[-1]] < nums[i]:
            idx = second_greater.pop()
            answer[idx] = nums[i]
        
        temp = deque()
        while first_greater and nums[first_greater[-1]] < nums[i]:
            idx = first_greater.pop()
            temp.appendleft(idx)
        
        while temp:
            second_greater.append(temp.popleft())
        
        first_greater.append(i)
    
    return answer",21,True,51,51,747 ms,"**Tests that the code passed:**

assert secondGreaterElement([2,4,0,9,6]) == [9,6,6,-1,-1]
assert secondGreaterElement([3,3]) == [-1,-1]

**Tests that the code failed:**

No tests failed.
","(True, True)",1
300,minimum-number-of-operations-to-make-arrays-similar,"
def makeSimilar(nums: List[int], target: List[int]) -> int:
    from collections import Counter
    
    nums.sort()
    target.sort()
    
    nums_even = [x for x in nums if x % 2 == 0]
    nums_odd = [x for x in nums if x % 2 != 0]
    target_even = [x for x in target if x % 2 == 0]
    target_odd = [x for x in target if x % 2 != 0]
    
    def count_operations(source, target):
        operations = 0
        for s, t in zip(source, target):
            if s < t:
                operations += (t - s) // 2
        return operations
    
    return count_operations(nums_even, target_even) + count_operations(nums_odd, target_odd)
",21,True,32,32,720 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
301,minimum-number-of-operations-to-make-arrays-similar,"def makeSimilar(nums: List[int], target: List[int]) -> int:
    from collections import Counter
    
    # Sort the input lists
    nums.sort()
    target.sort()
    
    # Separate even and odd numbers
    nums_even = [x for x in nums if x % 2 == 0]
    nums_odd = [x for x in nums if x % 2 != 0]
    target_even = [x for x in target if x % 2 == 0]
    target_odd = [x for x in target if x % 2 != 0]
    
    def count_operations(source, target):
        operations = 0
        for s, t in zip(source, target):
            if s < t:
                operations += (t - s) // 2
        return operations
    
    # Combine the operations for even and odd lists
    return count_operations(nums_even, target_even) + count_operations(nums_odd, target_odd)",21,True,32,32,718 ms,"**Tests that the code passed:**

assert makeSimilar([8,12,6], [2,14,10]) == 2
assert makeSimilar([1,2,5], [4,1,3]) == 1
assert makeSimilar([1,1,1,1,1], [1,1,1,1,1]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
302,minimum-cost-to-make-array-equal,"
from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    left, right = min(nums), max(nums)
    while left < right:
        mid = (left + right) // 2
        cost1 = get_cost(mid)
        cost2 = get_cost(mid + 1)
        
        if cost1 < cost2:
            right = mid
        else:
            left = mid + 1
    
    return get_cost(left)
",21,True,48,48,595 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
303,minimum-cost-to-make-array-equal,"from typing import List

def minCost(nums: List[int], cost: List[int]) -> int:
    def get_cost(target):
        return sum(abs(num - target) * c for num, c in zip(nums, cost))
    
    # Edge case: If all elements in nums are the same, return 0
    if len(set(nums)) == 1:
        return 0
    
    left, right = min(nums), max(nums)
    while left < right:
        mid = (left + right) // 2
        cost1 = get_cost(mid)
        cost2 = get_cost(mid + 1)
        
        if cost1 < cost2:
            right = mid
        else:
            left = mid + 1
    
    return get_cost(left)",21,True,48,48,624 ms,"**Tests that the code passed:**

assert minCost([1,3,5,2], [2,3,1,14]) == 8
assert minCost([2,2,2,2,2], [4,2,8,1,3]) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",1
304,create-components-with-same-value,"
from typing import List
from collections import defaultdict, deque

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = [False] * n
        def dfs(node):
            visited[node] = True
            total = nums[node]
            for neighbor in graph[node]:
                if not visited[neighbor]:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total > target:
                return -1
            return total if total < target else 0
        
        for i in range(n):
            if not visited[i]:
                if dfs(i) != 0:
                    return False
        return True

    n = len(nums)
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    for k in range(n, 0, -1):
        if total_sum % k == 0:
            if can_split(total_sum // k):
                return k - 1
    return 0
",21,True,43,43,1162 ms,"**Tests that the code passed:**

assert componentValue([6,2,2,2,6], [[0,1],[1,2],[1,3],[3,4]]) == 2
assert componentValue([2], []) == 0

**Tests that the code failed:**

No tests failed.
","(True, True)",0
305,create-components-with-same-value,"from typing import List
from collections import defaultdict

def componentValue(nums: List[int], edges: List[List[int]]) -> int:
    def can_split(target):
        visited = set()
        memo = {}

        def dfs(node):
            if node in memo:
                return memo[node]
            visited.add(node)
            total = nums[node]
            for neighbor in graph[node]:
                if neighbor not in visited:
                    result = dfs(neighbor)
                    if result == -1:
                        return -1
                    total += result
            if total == target:
                memo[node] = 0
                return 0
            if total > target:
                return -1
            memo[node] = total
            return total

        for i in range(n):
            if i not in visited:
                if dfs(i) != 0:
                    return False
        return True

    n = len(nums)
    graph = defaultdict(list)
    for u, v in edges:
        graph[u].append(v)
        graph[v].append(u)
    
    total_sum = sum(nums)
    divisors = [i for i in range(1, int(total_sum**0.5) + 1) if total_sum % i == 0]
    divisors += [total_sum // i for i in divisors if i != total_sum // i]
    divisors = sorted(divisors)

    for k in divisors:
        if can_split(total_sum // k):
            return k - 1
    return 0",21,False,35,43,,,,1
306,count-subarrays-with-fixed-bounds,"
def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = left_bound = -1

    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            left_bound = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        count += max(0, min(min_pos, max_pos) - left_bound)
    
    return count
",21,True,52,52,703 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",0
307,count-subarrays-with-fixed-bounds,"from typing import List

def countSubarrays(nums: List[int], minK: int, maxK: int) -> int:
    count = 0
    min_pos = max_pos = left_bound = -1

    for i, num in enumerate(nums):
        if num < minK or num > maxK:
            left_bound = i
        if num == minK:
            min_pos = i
        if num == maxK:
            max_pos = i
        if min_pos != -1 and max_pos != -1:
            count += max(0, min(min_pos, max_pos) - left_bound)
    
    return count",21,True,52,52,676 ms,"**Tests that the code passed:**

assert countSubarrays([1,3,5,2,7,5], 1, 5) == 2
assert countSubarrays([1,1,1,1], 1, 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True)",1
308,longest-increasing-subsequence-ii,"
from typing import List
import bisect

def lengthOfLIS(nums: List[int], k: int) -> int:
    dp = []
    
    for num in nums:
        pos = bisect.bisect_left(dp, num)
        if pos == len(dp):
            dp.append(num)
        elif num - (dp[pos - 1] if pos > 0 else float('-inf')) <= k:
            dp[pos] = num
    
    return len(dp)
",21,False,47,84,,"**Tests that the code passed:**

No tests passed.


**Tests that the code failed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5 # ERROR: This unit test fails. Output was 4, but expected value was: 5
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4 # ERROR: This unit test fails. Output was 3, but expected value was: 4
assert lengthOfLIS([1,5], 1) == 1 # ERROR: This unit test fails. Output was 2, but expected value was: 1","(False, False, False)",0
309,longest-increasing-subsequence-ii,"from typing import List

def lengthOfLIS(nums: List[int], k: int) -> int:
    if not nums:
        return 0
    
    dp = [1] * len(nums)
    
    for i in range(len(nums)):
        for j in range(i):
            if nums[i] > nums[j] and nums[i] - nums[j] <= k:
                dp[i] = max(dp[i], dp[j] + 1)
    
    return max(dp)",21,False,71,84,,"**Tests that the code passed:**

assert lengthOfLIS([4,2,1,4,3,4,5,8,15], 3) == 5
assert lengthOfLIS([7,4,5,1,8,12,4,7], 5) == 4
assert lengthOfLIS([1,5], 1) == 1

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
310,paths-in-matrix-whose-sum-is-divisible-by-k,"
from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # dp[i][j][r] will store the number of paths to (i, j) with sum % k == r
    dp = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp[i][j][r] > 0:
                    if i + 1 < m:
                        new_r = (r + grid[i + 1][j]) % k
                        dp[i + 1][j][new_r] = (dp[i + 1][j][new_r] + dp[i][j][r]) % MOD
                    if j + 1 < n:
                        new_r = (r + grid[i][j + 1]) % k
                        dp[i][j + 1][new_r] = (dp[i][j + 1][new_r] + dp[i][j][r]) % MOD
    
    return dp[m - 1][n - 1][0]
",21,True,88,88,2246 ms,"**Tests that the code passed:**

assert numberOfPaths([[5,2,4],[3,0,5],[0,7,2]], 3) == 2
assert numberOfPaths([[0,0]], 5) == 1
assert numberOfPaths([[7,3,4,9],[2,3,6,2],[2,3,7,0]], 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",0
311,paths-in-matrix-whose-sum-is-divisible-by-k,"from typing import List

def numberOfPaths(grid: List[List[int]], k: int) -> int:
    MOD = 10**9 + 7
    m, n = len(grid), len(grid[0])
    
    # dp[i][j][r] will store the number of paths to (i, j) with sum % k == r
    dp = [[[0] * k for _ in range(n)] for _ in range(m)]
    dp[0][0][grid[0][0] % k] = 1
    
    for i in range(m):
        for j in range(n):
            for r in range(k):
                if dp[i][j][r] > 0:
                    if i + 1 < m:
                        new_r = (r + grid[i + 1][j]) % k
                        dp[i + 1][j][new_r] = (dp[i + 1][j][new_r] + dp[i][j][r]) % MOD
                    if j + 1 < n:
                        new_r = (r + grid[i][j + 1]) % k
                        dp[i][j + 1][new_r] = (dp[i][j + 1][new_r] + dp[i][j][r]) % MOD
    
    return dp[m - 1][n - 1][0] % MOD",21,True,88,88,2257 ms,"**Tests that the code passed:**

assert numberOfPaths([[5,2,4],[3,0,5],[0,7,2]], 3) == 2
assert numberOfPaths([[0,0]], 5) == 1
assert numberOfPaths([[7,3,4,9],[2,3,6,2],[2,3,7,0]], 1) == 10

**Tests that the code failed:**

No tests failed.
","(True, True, True)",1
