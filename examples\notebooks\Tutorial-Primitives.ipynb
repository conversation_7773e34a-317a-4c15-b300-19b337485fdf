{"cells": [{"cell_type": "markdown", "id": "d66880e8-23d9-4f00-b120-7f289f682511", "metadata": {}, "source": ["# TextGrad Tutorials: Primitives\n", "\n", "![TextGrad](https://github.com/vinid/data/blob/master/logo_full.png?raw=true)\n", "\n", "An autograd engine -- for textual gradients!\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/zou-group/TextGrad/blob/main/examples/notebooks/Prompt-Optimization.ipynb)\n", "[![GitHub license](https://img.shields.io/badge/License-MIT-blue.svg)](https://lbesson.mit-license.org/)\n", "[![Arxiv](https://img.shields.io/badge/arXiv-2406.07496-B31B1B.svg)](https://arxiv.org/abs/2406.07496)\n", "[![Documentation Status](https://readthedocs.org/projects/textgrad/badge/?version=latest)](https://textgrad.readthedocs.io/en/latest/?badge=latest)\n", "[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/textgrad)](https://pypi.org/project/textgrad/)\n", "[![PyPI](https://img.shields.io/pypi/v/textgrad)](https://pypi.org/project/textgrad/)\n", "\n", "**Objectives for this tutorial:**\n", "\n", "* Introduce you to the primitives in TextGrad\n", "\n", "**Requirements:**\n", "\n", "* You need to have an OpenAI API key to run this tutorial. This should be set as an environment variable as OPENAI_API_KEY.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "36a9c615-17a0-455c-8f9c-f0d25fb8824b", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:43:10.594204491Z", "start_time": "2024-06-11T15:43:10.589328053Z"}}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["!pip install textgrad # you might need to restart the notebook after installing textgrad\n", "\n", "from textgrad.engine import get_engine\n", "from textgrad import Variable\n", "from textgrad.optimizer import TextualGradientDescent\n", "from textgrad.loss import TextLoss\n", "from dotenv import load_dotenv\n", "load_dotenv()"]}, {"cell_type": "markdown", "id": "8887fbed36c7daf2", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Introduction: Variable\n", "\n", "Variables in TextGrad are the metaphorical equivalent of tensors in PyTorch. They are the primary data structure that you will interact with when using TextGrad. \n", "\n", "Variables keep track of gradients and manage the data.\n", "\n", "Variables require two arguments (and there is an optional third one):\n", "\n", "1. `data`: The data that the variable will hold\n", "2. `role_description`: A description of the role of the variable in the computation graph\n", "3. `requires_grad`: (optional) A boolean flag that indicates whether the variable requires gradients"]}, {"cell_type": "code", "execution_count": 14, "id": "c65fb4456d84c8fc", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:43:17.669096228Z", "start_time": "2024-06-11T15:43:17.665325560Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["x = Variable(\"A sntence with a typo\", role_description=\"The input sentence\", requires_grad=True)"]}, {"cell_type": "code", "execution_count": 15, "id": "65857dd50408ebd7", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:43:18.184004948Z", "start_time": "2024-06-11T15:43:18.178187640Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["x.gradients"]}, {"cell_type": "markdown", "id": "63f6a6921a1cce6a", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Introduction: Engine\n", "\n", "When we talk about the engine in TextGrad, we are referring to an LLM. The engine is an abstraction we use to interact with the model."]}, {"cell_type": "code", "execution_count": 24, "id": "281644022ac1c65d", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:44:32.606319032Z", "start_time": "2024-06-11T15:44:32.561460448Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["engine = get_engine(\"gpt-3.5-turbo\")"]}, {"cell_type": "markdown", "id": "33c7d6eaa115cd6a", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["This object behaves like you would expect an LLM to behave: You can sample generation from the engine using the `generate` method. "]}, {"cell_type": "code", "execution_count": 29, "id": "37502bf67ef23c53", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T17:29:41.108552705Z", "start_time": "2024-06-11T17:29:40.294256814Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["\"Hello! I'm here and ready to assist you. How can I help you today?\""]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["engine.generate(\"Hello how are you?\")"]}, {"cell_type": "markdown", "id": "b627edc07c0d3737", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Introduction: Loss\n", "\n", "Again, Loss in TextGrad is the metaphorical equivalent of loss in PyTorch. We use Losses in different form in TextGrad but for now we will focus on a simple TextLoss. TextLoss is going to evaluate the loss wrt a string."]}, {"cell_type": "code", "execution_count": 25, "id": "252e0a0152b81f14", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:44:32.894722136Z", "start_time": "2024-06-11T15:44:32.890708561Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["system_prompt = Variable(\"Evaluate the correctness of this sentence\", role_description=\"The system prompt\")\n", "loss = TextLoss(system_prompt, engine=engine)"]}, {"cell_type": "markdown", "id": "ff137c99e0659dcc", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": []}, {"cell_type": "markdown", "id": "6f05ec2bf907b3ba", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Introduction: Optimizer\n", "\n", "Keeping on the analogy with PyTorch, the optimizer in TextGrad is the object that will update the parameters of the model. In this case, the parameters are the variables that have `requires_grad` set to `True`.\n", "\n", "**NOTE** This is a text optimizer! It will do all operations with text! "]}, {"cell_type": "code", "execution_count": 26, "id": "78f93f80b9e3ad36", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:44:33.741130951Z", "start_time": "2024-06-11T15:44:33.734977769Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["optimizer = TextualGradientDescent(parameters=[x], engine=engine)\n"]}, {"cell_type": "markdown", "id": "d26883eb74ce0d01", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Putting it all together\n", "\n", "We can now put all the pieces together. We have a variable, an engine, a loss, and an optimizer. We can now run a single optimization step."]}, {"cell_type": "code", "execution_count": 27, "id": "9817e0ae0179376d", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:44:41.730132530Z", "start_time": "2024-06-11T15:44:34.997777872Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["l = loss(x)\n", "l.backward(engine)\n", "optimizer.step()"]}, {"cell_type": "code", "execution_count": 28, "id": "77e3fab0efdd579e", "metadata": {"ExecuteTime": {"end_time": "2024-06-11T15:44:41.738985151Z", "start_time": "2024-06-11T15:44:41.731989729Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["'A sentence with a typo'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["x.value"]}, {"cell_type": "markdown", "id": "6a8aab93b80fb82c", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["While here it is not going to be useful, we can also do multiple optimization steps in a loop! Do not forget to reset the gradients after each step!"]}, {"cell_type": "code", "execution_count": null, "id": "d6bb8d0dcc2539d1", "metadata": {"ExecuteTime": {"start_time": "2024-06-11T15:44:30.989940227Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["optimizer.zero_grad()"]}, {"cell_type": "code", "execution_count": null, "id": "a3a84aad4cd58737", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}